package config

import (
	"encoding/json"
	"fmt"
	"os"
	"os/user"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"

	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xutils"
)

var cfg *Config

// 可通过链接器修改的变量
var (
	DeployLocalStr = "false" //是否本地部署，字符串形式，通过链接器设置
	DeployZhiYuan  = false   //一些特殊修改，深圳市智源空间

	DeployLocal bool
)

func ParseConfig(filename string) {
	xlog.Info("parse config file : " + filename)
	viper.SetConfigFile(filename)
	viper.AddConfigPath(".")
	err := viper.ReadInConfig()
	if err != nil {
		fmt.Println("readin failed:", err)
		return
	}

	cfg = &Config{}
	err = viper.Unmarshal(cfg, func(config *mapstructure.DecoderConfig) {
		config.TagName = "yaml"
	})
	if err != nil {
		xlog.Error("yaml unmarshal failed: ", err)
		os.Exit(1)
	}

	DeployLocal = DeployLocalStr == "true"
	fmt.Println("=================================", "deployLocal", DeployLocal)

	//拼接路径
	// cfg.Misc.CDNDir = strings.TrimSpace(cfg.Misc.CDNDir)
	// cfg.Misc.PathFirmware = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathFirmware))
	// cfg.Misc.PathImage = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathImage))
	// cfg.Misc.PathExport = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathExport))
	// cfg.Misc.PathLog = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathLog))
	// cfg.Misc.PathTmp = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathTmp))

	cfg.Misc.CDNDir = "cdn"
	cfg.Misc.PathExport = "cdn/export"
	cfg.Misc.PathFirmware = "cdn/firmware"
	cfg.Misc.PathImage = "cdn/images"
	cfg.Misc.PathTmp = "cdn/tmp"
	cfg.Misc.PathLog = "cdn/logs"
	// xlog.Debug("config misc", "misc", cfg.Misc)

	//在不同的机器上运行的时候，请保证有一些字符串，必须不可以重复。包括：

	//每个服务的 mqtt_client_id
	nonce := strings.ToLower(xutils.RandStringN(10))

	cfg.ServiceGW.MqttClientIDAdapter = fmt.Sprintf("%s_%s", cfg.ServiceGW.MqttClientIDAdapter, nonce)
	cfg.ServiceGW.MqttClientIDIot = fmt.Sprintf("%s_%s", cfg.ServiceGW.MqttClientIDIot, nonce)
	if cfg.ServiceProxy != nil {
		cfg.ServiceProxy.MqttClientID = fmt.Sprintf("%s_%s", cfg.ServiceProxy.MqttClientID, nonce)
	}
	if DeployLocal {
		cfg.Misc.LogLevel = "error"
		cfg.ServiceGW.DBLogLevel = "error"
	}
	xlog.Debug("parse config")
}

func ParseConfigForVd(filename string) {
	xlog.Info("parse config file : " + filename)
	viper.SetConfigFile(filename)
	viper.AddConfigPath(".")
	err := viper.ReadInConfig()
	if err != nil {
		xlog.Error("readin failed: " + err.Error())
		return
	}

	cfg = &Config{}
	err = viper.Unmarshal(cfg, func(config *mapstructure.DecoderConfig) {
		config.TagName = "yaml"
	})
	if err != nil {
		xlog.Error("yaml unmarshal failed: " + err.Error())
		os.Exit(1)
	}
	xlog.Debug("parse config")
}

func InitMisc() {
	//拼接路径
	//cfg.Misc.CDNDir = filepath.Join(xutils.GetExecPath(), strings.TrimSpace(cfg.Misc.CDNDir))
	cfg.Misc.CDNDir = strings.TrimSpace(cfg.Misc.CDNDir)

	cfg.Misc.PathFirmware = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathFirmware))
	cfg.Misc.PathImage = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathImage))
	cfg.Misc.PathExport = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathExport))
	cfg.Misc.PathLog = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathLog))
	cfg.Misc.PathTmp = filepath.Join(strings.TrimSpace(cfg.Misc.CDNDir), strings.TrimSpace(cfg.Misc.PathTmp))
}

func getCurrentUsername() string {
	me, err := user.Current() //3、再次，使用当前用户名
	if err != nil {
		return runtime.GOOS //4、最后，使用当前系统类型
	}
	return me.Username
}

func prettyDump(v interface{}) string {
	//b, _ := json.Marshal(v)
	b, _ := json.MarshalIndent(v, "", "  ")
	return string(b)
}

func Get() *Config {
	return cfg
}

func GetMqtt() (host string, hbtimeout int) {
	return cfg.Mqtt.MqttBrokerHost, cfg.Mqtt.MqttHeartbeatTimeout
}

func GetNatsUrl() string {
	return cfg.Nats.Url
}
