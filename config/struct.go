package config

type Config struct {
	DeployID      string `yaml:"deploy_id"          json:"deploy_id"`
	DeployLicense string `yaml:"deploy_license"     json:"deploy_license"`

	//==============================================================================
	//http 网关
	ServiceGW *configServiceGW `yaml:"service_gw" json:"service_gw"`

	// http 子服务
	ServiceBuca *configHttpService `yaml:"service_buca" json:"service_buca"`
	//==============================================================================
	//sl651服务
	ServiceSl651 *ConfigTcpService `yaml:"service_sl651" json:"service_sl651"`
	//==============================================================================
	//sl2022服务
	ServiceSl2022 *ConfigTcpService `yaml:"service_sl2022" json:"service_sl2022"`
	// ==============================================================================
	// shui206 服务
	ServiceSzy206 *ConfigTcpService `yaml:"service_szy206" json:"service_szy206"`
	ServiceSL427  *ConfigTcpService `yaml:"service_sl427" json:"service_sl427"`
	ServiceTcps   *ConfigTcpService `yaml:"service_tcps" json:"service_tcps"`
	ServiceUdps   *ConfigUdpService `yaml:"service_udps" json:"service_udps"`
	//==============================================================================
	ServiceProxy *configServiceProxy `yaml:"service_proxy" json:"service_proxy"`
	//==============================================================================
	// gb28181信令服务器参数
	Sip     *sipServerConfig  `yaml:"sip" json:"sip"`
	Zlmedia *ZLMediaKitConfig `yaml:"zlmedia"  json:"zlmedia"`
	//==============================================================================

	Vd1800 configVd1800 `yaml:"vd1800" json:"vd1800"`
	VdTCP  configVdTCP  `yaml:"vd_tcp" json:"vd_tcp"`
	//其他公共配置
	Misc Misc `yaml:"misc"  json:"misc"` //其他配置

	//第三方服务相关配置
	Aliyun    Aliyun          `yaml:"aliyun"  json:"aliyun"` //阿里云服务
	AMap      AMap            `yaml:"amap"  json:"amap"`     //高德地图服务
	Minio     MinioConfig     `yaml:"minio" json:"minio"`    // 文件服务器
	Nats      NatsConfig      `yaml:"nats" json:"nats"`      // nats服务
	Mqtt      MqttConfig      `yaml:"mqtt" json:"mqtt"`      // nats服务
	Mysql     MysqlConfig     `yaml:"mysql" json:"mysql"`    // mysql
	Influxdb2 Influxdb2Config `yaml:"influxdb2" json:"influxdb2"`
	Redis     RedisConfig     `yaml:"redis" json:"redis"` // redis

	Postgres PostgresConfig `yaml:"postgres" json:"postgres"` // postgres

	//Etcd EtcdConfig `yaml:"etcd" json:"etcd"` //etcd
	SimIOT SimIOTConf `yaml:"simiot"   json:"simiot"`
}

// ===========================================================================================================
type configVd1800 struct {
	Name             string `yaml:"name" json:"name"`                           //server name
	HeartbeatTimeout int    `yaml:"heartbeat_timeout" json:"heartbeat_timeout"` //心跳间隔
	IsDemoBatch      bool   `yaml:"is_demo_batch" json:"is_demo_batch"`         //批量测试用文件里面的虚拟设备数据，非批量数据用配置文件里面的数据
	ProxyHost        string `yaml:"proxy_host"  json:"proxy_host"`              //代理服务器地址 ip:port。只有这一个
}

type configVdTCP struct {
	Name             string `yaml:"name" json:"name"`                           //server name
	HeartbeatTimeout int    `yaml:"heartbeat_timeout" json:"heartbeat_timeout"` //心跳间隔
	IotHost          string `yaml:"iot_host"   json:"iot_host"`                 //运维服务器地址 ip:port
	Shui651Host      string `yaml:"shui651_host"  json:"shui651_host"`          //水利服务器地址 ip:port， SL651 协议
	Shui206Host      string `yaml:"shui206_host"  json:"shui206_host"`          //水利服务器地址 ip:port， SZY206 协议
	Shui427Host      string `yaml:"shui427_host"  json:"shui427_host"`          //水利服务器地址 ip:port， SL427 协议
	Shui2022Host     string `yaml:"shui2022_host"  json:"shui2022_host"`        //水利服务器地址 ip:port， SL2022 协议

	IsDemoBatch          bool   `yaml:"is_demo_batch" json:"is_demo_batch"`                   //批量测试用文件里面的虚拟设备数据，非批量数据用配置文件里面的数据
	MqttBrokerHost       string `yaml:"mqtt_broker_host" json:"mqtt_broker_host"`             //地址
	MqttHeartbeatTimeout int    `yaml:"mqtt_heartbeat_timeout" json:"mqtt_heartbeat_timeout"` //心跳间隔
}

// ===========================================================================================================
// gateway: http,iot_mcu_tcp, iot_linux_mqtt
type configServiceGW struct {
	Name                string `yaml:"name"            json:"name"`                           //server name
	HttpPort            uint16 `yaml:"http_port"       json:"http_port"`                      //http port
	TcpPort             uint16 `yaml:"tcp_port"        json:"tcp_port"`                       //tcp port
	MqttClientIDAdapter string `yaml:"mqtt_client_id_adapter"  json:"mqtt_client_id_adapter"` //cloud mqtt clientID for adapter
	MqttClientIDIot     string `yaml:"mqtt_client_id_iot"  json:"mqtt_client_id_iot"`         //cloud mqtt clientID for iot
	DBLogLevel          string `yaml:"db_log_level"    json:"db_log_level"`                   //默认gorm日志等级，当设置为 debug，开启  gorm debug 模式
	WSDeviceReport      bool   `yaml:"ws_device_report"  json:"ws_device_report"`             // 设备上报数据时候，是否上报到websocket
	ForwardURL          string `yaml:"forward_url"  json:"forward_url"`                       // 如有数据转发需求，设置这里
	AutoAddDatapoint    bool   `yaml:"auto_add_datapoint"  json:"auto_add_datapoint"`         // 是否自动根据解析的数据添加测点
}

//===========================================================================================================

type configHttpService struct {
	Name string `yaml:"name" json:"name"` //server name
	Host string `yaml:"host" json:"host"` //server host,ip+port or domain+port or container_name+port
}

// sl651 server, use sl651
type ConfigTcpService struct {
	Name             string `yaml:"name" json:"name"`                           //server name
	Port             uint16 `yaml:"port" json:"port"`                           //port
	HeartbeatTimeout int    `yaml:"heartbeat_timeout" json:"heartbeat_timeout"` //心跳间隔
	HeartbeatPacket  bool   `yaml:"heartbeat_packet" json:"heartbeat_packet"`   //是否记录心跳包原始报文，调试用，默认 false
	Version          string `yaml:"version" json:"version"`                     //版本
}

type ConfigUdpService struct {
	Name             string `yaml:"name" json:"name"`                           //server name
	Port             uint16 `yaml:"port" json:"port"`                           //port
	HeartbeatTimeout int    `yaml:"heartbeat_timeout" json:"heartbeat_timeout"` //心跳间隔
	HeartbeatPacket  bool   `yaml:"heartbeat_packet" json:"heartbeat_packet"`   //是否记录心跳包原始报文，调试用，默认 false
}

// Only For NC1800
type configServiceProxy struct {
	Name      string `yaml:"name" json:"name"`             //server name
	Port      uint16 `yaml:"port" json:"port"`             //port
	ProductID string `yaml:"product_id" json:"product_id"` //虚拟设备产品ID
	HostSl651 string `yaml:"host_sl651" json:"host_sl651"` //默认此接入服务器分发 sl651 数据的目的主机 IP 地址

	//cloud mqtt clientID,分发 iot 数据
	MqttClientID string `yaml:"mqtt_client_id" json:"mqtt_client_id"`
}

// =================================================================================================
type Misc struct {
	CDNDir       string `yaml:"cdn_dir" json:"cdn_dir"`             //静态文件服务的根目录
	PathFirmware string `yaml:"path_firmware" json:"path_firmware"` //软件文件
	PathImage    string `yaml:"path_image" json:"path_image"`       //图片文件
	PathExport   string `yaml:"path_export" json:"path_export"`     //整编文件地址
	PathTmp      string `yaml:"path_tmp" json:"path_tmp"`           //临时文件地址
	PathLog      string `yaml:"path_log" json:"path_log"`           //日志文件

	LogLevel             string `yaml:"log_level" json:"log_level"`                             //默认日志等级
	Format               string `yaml:"format" json:"format"`                                   //默认日志格式
	Sl651ResponseWithEOT bool   `yaml:"sl651_response_with_eot" json:"sl651_response_with_eot"` //默认日志格式
	SL651WithMQTT        bool   `yaml:"sl651_with_mqtt" json:"sl651_with_mqtt"`                 //是否通过 mqtt，推送数据，only for 岳阳山洪
}

// 阿里云：短信、文件存储
type Aliyun struct {
	OssBucketImage string `yaml:"oss_bucket_image" json:"oss_bucket_image"` //存放设备图像
	OssBucketOther string `yaml:"oss_bucket_other" json:"oss_bucket_other"` //存放其他文件
	//Endpoint        string `yaml:"endpoint" json:"endpoint"`
	//AccessKeyId     string `yaml:"access_key_id" json:"access_key_id"`
	//AccessKeySecret string `yaml:"access_key_secret" json:"access_key_secret"`
}

// 高德地图
type AMap struct {
	AmapKey string `yaml:"amap_key" json:"amap_key"`
}

// minio
type MinioConfig struct {
	Endpoint      string `yaml:"endpoint" json:"endpoint"`
	Domain        string `yaml:"domain" json:"domain"` // 文件外部访问的url域名前缀
	AccessKey     string `yaml:"access_key" json:"access_key"`
	SecretKey     string `yaml:"secret_key" json:"secret_key"`
	BucketImage   string `yaml:"bucket_image" json:"bucket_image"`      //存放设备图像
	BucketOther   string `yaml:"bucket_other" json:"bucket_other"`      //存放其他文件
	BucketProject string `yaml:"bucket_project"  json:"bucket_project"` // 存放project相关文件(用户可读写)
}

// nats
type NatsConfig struct {
	Url string `yaml:"url" json:"url"`
}

type MqttConfig struct {
	MqttBrokerHost       string `yaml:"mqtt_broker_host" json:"mqtt_broker_host"`             //地址
	MqttHeartbeatTimeout int    `yaml:"mqtt_heartbeat_timeout" json:"mqtt_heartbeat_timeout"` //心跳间隔
}

// mysql
type MysqlConfig struct {
	Address  string `yaml:"address"  json:"address"`
	Password string `yaml:"password" json:"password"`
	DBName   string `yaml:"db_name"  json:"db_name"`
}

type PostgresConfig struct {
	Address  string `yaml:"address"  json:"address"`
	Username string `yaml:"username" json:"username"`
	Password string `yaml:"password" json:"password"`
	DBName   string `yaml:"db_name"  json:"db_name"`
}

type Influxdb2Config struct {
	Address string `yaml:"address"      json:"address"`
	Token   string `yaml:"token"        json:"token"`
	Org     string `yaml:"org"          json:"org"`
	Bucket  string `yaml:"bucket"       json:"bucket"`
}

// redis
type RedisConfig struct {
	Address  string `yaml:"address" json:"address"`
	Password string `yaml:"password" json:"password"`
}

//// etcd
//type EtcdConfig struct {
//	Address  string `yaml:"address" json:"address"`
//	BasePath string `yaml:"base_path" json:"base_path"`
//}

type SimIOTConf struct {
	Api    string `yaml:"api"  json:"api"`
	AppID  string `yaml:"appid" json:"appid"`
	Secret string `yaml:"secret" json:"secret"`
}

// gb28181的信令服务器配置
type sipServerConfig struct {
	//   server_port: "60060"
	//   password: "987666"
	//   server_domain: "3402000000"
	//   server_id: "34020000002000000001"
	ServerPort   string `yaml:"server_port"     json:"server_port"`
	ServerDomain string `yaml:"server_domain"   json:"server_domain"`
	ServerID     string `yaml:"server_id"       json:"server_id"`
	Password     string `yaml:"password"        json:"password"`
}

// zlmedia的配置
type ZLMediaKitConfig struct {
	// port: 55080
	// host: zlmedia
	Port string `yaml:"port"   json:"port"`
	Host string `yaml:"host"   json:"host"`
}
