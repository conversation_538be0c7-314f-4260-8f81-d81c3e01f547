<!DOCTYPE HTML>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0" name="viewport" />

    <title>跳转至应用下载</title>


    <style>
        body,
        html {
            width: 100%;
            height: 100%
        }
 
        * {
            margin: 0;
            padding: 0
        }
    </style>

</head>

<body>
 
    <h2 style="padding: 50px 0; text-align: center;">下载应用</h2>
    <div style="padding:30px 10%;">
        <div id="ok" style="display: none;" >
            <h3 style="color: dodgerblue;">点击右上角，用默认浏览器打开，即可下载安装。</h3>
            <img src="live_wechat.png" style="max-width: 100%; height: auto;">
        </div>


        <h2 id="tip" style="color:dodgerblue; display: none;">暂时仅支持 Android 平台。</h2>
    </div>

    <script type="text/javascript">

        var domain  = "https://nuc.beithing.com"

        var getLastUrl =  domain + ":60080/api/not_login/app/last_version";
        var downBaseUrl =  domain + ":59000"

        // 获取终端的相关信息
        var Terminal = {
            // 辨别移动终端类型
            platform: function () {
                var u = navigator.userAgent, app = navigator.appVersion;
                return {
                    // android终端或者uc浏览器
                    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,
                    // 是否为iPhone或者QQHD浏览器
                    iPhone: u.indexOf('iPhone') > -1,
                    // 是否iPad
                    iPad: u.indexOf('iPad') > -1
                };
            }(),
            // 辨别移动终端的语言：zh-cn、en-us、ko-kr、ja-jp...
            language: (navigator.browserLanguage || navigator.language).toLowerCase()
        }


    function isWechat() {
        var ua = window.navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
            return true;
        } else {
            return false;
        }
    }

    console.log("gogogo .....")
    if (isWechat()) {
        console.log("is wechat")
        if (Terminal.platform.android) {
            var ok = document.getElementById('ok');
            ok.style.display = 'block';
        }else{
            console.log("暂时仅支持 Android 平台")
            var tip = document.getElementById('tip');
            tip.style.display = 'block';
        }
    }else{
        console.log("not wechat")
        if (Terminal.platform.android) {
            //安卓端
            /* theUrl = 'https://nuc.beithing.com:60080/cdn/app/app-latest.apk'; */
            //  location.href = theUrl;
            // 
            var httpRequest = new XMLHttpRequest();//第一步：建立所需的对象
            
            httpRequest.open('GET', getLastUrl, true);//第二步：打开连接  
            httpRequest.send();//第三步：发送请求  将请求参数写在URL中
            /**
             * 获取数据后的处理程序
             */
            httpRequest.onreadystatechange = function () {
                if (httpRequest.readyState == 4 && httpRequest.status == 200) {
                    var resp = httpRequest.responseText;//获取到json字符串，还需解析
                    var respjson = JSON.parse(resp)
                    var downurl = respjson.payload.http_url;
                    var theUrl = downBaseUrl +  downurl;
                    location.href = theUrl;
                }
            };

        } else {
            console.log("暂时仅支持 Android 平台")
        }
    }

    </script>
</body>

</html>
