[{"id": 10, "created_at": "2023-07-14T10:54:48.037689+08:00", "updated_at": "2024-04-30T21:09:53.749486+08:00", "key": "crvonfe434m6", "secret": "KvtWBV8RLgMlHL2SfDfK5Z9CSehYYL6OGX4QzrpKbtdE5D0C8VxWoy3tsQPer3sA", "model": "RTUV2M8", "name": "RTUV2M8", "meta": {"DI": {"data_type": "ENUM", "default": 1, "name": "DI", "options": [{"name": "数字输入1", "value": 1}, {"name": "数字输入2", "value": 2}], "title": "数字输入"}, "MODBUS": {"data_type": "INTEGER", "default": "10", "name": "MODBUS", "title": "Modbus 通道数"}, "RS485": {"data_type": "ENUM", "default": 1, "name": "RS485", "options": [{"name": "RS485_1", "value": 1}, {"name": "RS485_2", "value": 2}, {"name": "RS485_3", "value": 3}], "title": "RS485"}, "VOUT": {"data_type": "ENUM", "default": 1, "name": "VOUT", "options": [{"name": "电源输出1", "value": 1}, {"name": "电源输出2", "value": 2}, {"name": "电源输出3", "value": 3}], "title": "电源输出"}, "VWP": {"data_type": "INTEGER", "default": "8", "name": "VWP", "options": [], "title": "振弦通道数"}}, "desc": "第二代 RTU，带8路振弦功能。", "is_diff": true}, {"id": 6, "created_at": "2023-01-11T05:53:07.511+08:00", "updated_at": "2024-04-30T21:09:53.753212+08:00", "key": "cpok6mjcd6l8", "secret": "fSJPoNkhwFhXVh5O7Sp63CbskUzhyW7yCzFsqK7JQhQ7phJCdNRnQKefiyql7qT6", "model": "NC1900", "name": "低功耗网关", "meta": {}, "desc": "Linux系统网关产品", "is_diff": true}, {"id": 1, "created_at": "2021-11-09T19:55:38.286+08:00", "updated_at": "2024-04-30T21:09:53.75673+08:00", "key": "amck1z7dw9a8", "secret": "", "model": "NC1800", "name": "NC1800", "meta": {}, "desc": "", "is_diff": false}, {"id": 15, "created_at": "2024-08-08T15:00:15.423076+08:00", "updated_at": "2024-08-08T15:00:15.423076+08:00", "key": "d3ac4axsj7gq", "secret": "fMgh15y7MBPa8b3iEIWsn82VrZBmqwOJ8zC84j4SrfD9b0iul3FSEFd0Z1aJDpKj", "model": "RTUV3", "name": "RTUV3", "meta": {}, "desc": "第三代 RTU，优化了硬件电路，增加了接口。", "is_diff": true}, {"id": 14, "created_at": "2024-08-08T14:54:00.897247+08:00", "updated_at": "2024-08-08T15:00:50.179734+08:00", "key": "d3abzivtjex9", "secret": "1XXiqG0sGNrhQHOliMeh81RdcBFN16kXc4iLngYU5U7OSkF7GG3G4VH1Gq3n6awe", "model": "RTUV3M8", "name": "RTUV3M8", "meta": {}, "desc": "支持 8 个振弦式采集仪的遥测终端", "is_diff": true}, {"id": 13, "created_at": "2024-08-08T14:46:44.330178+08:00", "updated_at": "2024-08-08T15:00:56.858797+08:00", "key": "d3abtybsrm79", "secret": "ZmUdKztsYF515aE6nwqgIAtUj9NeTs00VcH4vHbbjTDo37wqQZOXNrKTw7KJacX4", "model": "MCU16", "name": "MCU16", "meta": {}, "desc": "16通道的振弦采集终端，作为 RS485从设备", "is_diff": true}, {"id": 11, "created_at": "2023-07-14T10:55:06.423008+08:00", "updated_at": "2024-08-08T15:01:08.951711+08:00", "key": "ct4e8172nf00", "secret": "tmQNgSi4pE5oBGgkoFW8oQdwkFb3p8ouW7vRglAH9ZwGrA8HRljUAWNhn62tRzE8", "model": "NC2800", "name": "RTUV2", "meta": {"DI": {"data_type": "ENUM", "default": 1, "desc": "可作为雨量计接口", "name": "DI", "options": [{"name": "DI_1", "value": 1}, {"name": "DI_2", "value": 2}], "title": "数字输入"}, "K": {"data_type": "ENUM", "default": 1, "name": "K", "options": [{"name": "K1", "value": 1}, {"name": "K2", "value": 2}], "title": "继电器"}, "MODBUS": {"data_type": "INTEGER", "default": "4", "name": "MODBUS", "title": "Modbus通道数"}, "RS485": {"data_type": "ENUM", "default": 1, "name": "RS485", "options": [{"name": "RS485_1", "value": 1}, {"name": "RS485_2", "value": 2}, {"name": "RS485_3", "value": 3}], "title": "RS485接口"}}, "desc": "第二代 RTU。", "is_diff": true}]