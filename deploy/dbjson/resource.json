[{"id": 4, "menu_id": 4, "parent_id": 0, "type": 1, "title": "统计分析", "icon": "icon-statistics", "route": "/statistics", "path": "/src/layout/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 5, "menu_id": 5, "parent_id": 0, "type": 1, "title": "后台管理", "icon": "icon-operationMaintenance", "route": "/surveyStation", "path": "/src/layout/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 6, "menu_id": 6, "parent_id": 0, "type": 1, "title": "预警管理", "icon": "icon-earlyWarning", "route": "/earlyWarning", "path": "/src/layout/index.vue", "sort_num": 8, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 8, "menu_id": 8, "parent_id": 0, "type": 1, "title": "运维管理", "icon": "icon-operationMaintenance", "route": "/operationMaintenance", "path": "/src/views/operationMaintenance/index.vue", "sort_num": 4, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 9, "menu_id": 9, "parent_id": 0, "type": 1, "title": "系统管理", "icon": "icon-system", "route": "/backstage", "path": "/src/layout/index.vue", "sort_num": 10, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 10, "menu_id": 10, "parent_id": 9, "type": 1, "title": "产品管理", "icon": "icon-chan<PERSON><PERSON><PERSON>", "route": "/backstage/product", "path": "/src/views/backstage/product/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 12, "menu_id": 12, "parent_id": 9, "type": 1, "title": "固件管理", "icon": "icon-gujianguan<PERSON>", "route": "/backstage/software", "path": "/src/views/backstage/software/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 13, "menu_id": 13, "parent_id": 9, "type": 1, "title": "用户管理", "icon": "icon-yong<PERSON><PERSON><PERSON><PERSON>", "route": "/backstage/user", "path": "/src/views/backstage/user/index.vue", "sort_num": 6, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 14, "menu_id": 14, "parent_id": 9, "type": 1, "title": "角色管理", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/backstage/role", "path": "/src/views/backstage/role/index.vue", "sort_num": 7, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 15, "menu_id": 15, "parent_id": 9, "type": 1, "title": "菜单管理", "icon": "icon-caida<PERSON><PERSON><PERSON>", "route": "/backstage/menu", "path": "/src/views/backstage/menu/index.vue", "sort_num": 8, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 16, "menu_id": 16, "parent_id": 9, "type": 1, "title": "操作日志", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/backstage/journal", "path": "/src/views/backstage/journal/index.vue", "sort_num": 10, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 23, "menu_id": 23, "parent_id": 10, "type": 2, "title": "产品新建", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:product_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 25, "menu_id": 25, "parent_id": 9, "type": 1, "title": "字典管理", "icon": "icon-zidianguan<PERSON>", "route": "/backstage/dict", "path": "/src/views/backstage/dict/index.vue", "sort_num": 9, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 26, "menu_id": 26, "parent_id": 10, "type": 2, "title": "产品编辑", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:product_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 31, "menu_id": 31, "parent_id": 12, "type": 2, "title": "软件新增", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:software_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 32, "menu_id": 32, "parent_id": 12, "type": 2, "title": "软件编辑", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:software_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 33, "menu_id": 33, "parent_id": 12, "type": 2, "title": "软件删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:software_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 34, "menu_id": 34, "parent_id": 12, "type": 2, "title": "软件差分", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:software_difference", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 35, "menu_id": 35, "parent_id": 13, "type": 2, "title": "用户新增", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:user_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 36, "menu_id": 36, "parent_id": 13, "type": 2, "title": "用户修改", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:user_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 37, "menu_id": 37, "parent_id": 13, "type": 2, "title": "用户删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:user_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 38, "menu_id": 38, "parent_id": 14, "type": 2, "title": "角色新增", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:role_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 39, "menu_id": 39, "parent_id": 14, "type": 2, "title": "角色修改", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:role_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 40, "menu_id": 40, "parent_id": 14, "type": 2, "title": "角色删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:role_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 41, "menu_id": 41, "parent_id": 15, "type": 2, "title": "菜单新增", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:menu_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 42, "menu_id": 42, "parent_id": 15, "type": 2, "title": "菜单修改", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:menu_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 43, "menu_id": 43, "parent_id": 15, "type": 2, "title": "菜单删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:menu_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 44, "menu_id": 44, "parent_id": 25, "type": 2, "title": "字典新增", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:dict_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 45, "menu_id": 45, "parent_id": 25, "type": 2, "title": "字典修改", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:dict_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 46, "menu_id": 46, "parent_id": 25, "type": 2, "title": "字典删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:dict_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 47, "menu_id": 47, "parent_id": 217, "type": 2, "title": "测站召测", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:surveyStation_callSurvey", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 48, "menu_id": 48, "parent_id": 217, "type": 2, "title": "测站整编", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:surveyStation_reorganization", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 49, "menu_id": 49, "parent_id": 217, "type": 2, "title": "测站拍照", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:surveyStation_photograph", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 50, "menu_id": 50, "parent_id": 217, "type": 2, "title": "测站照片", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:surveyStation_photo", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 51, "menu_id": 51, "parent_id": 217, "type": 2, "title": "测站编辑", "icon": "", "route": "", "path": "", "sort_num": 5, "permission": "permission:surveyStation_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 52, "menu_id": 52, "parent_id": 217, "type": 2, "title": "测站删除", "icon": "", "route": "", "path": "", "sort_num": 6, "permission": "permission:surveyStation_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 56, "menu_id": 56, "parent_id": 9, "type": 1, "title": "公司管理", "icon": "icon-gongsiguanli", "route": "/backstage/company", "path": "/src/views/backstage/company/index.vue", "sort_num": 4, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 57, "menu_id": 57, "parent_id": 9, "type": 1, "title": "部门管理", "icon": "", "route": "/backstage/department", "path": "/src/views/backstage/department/index.vue", "sort_num": 5, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 58, "menu_id": 58, "parent_id": 56, "type": 2, "title": "新增公司", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:company_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 59, "menu_id": 59, "parent_id": 56, "type": 2, "title": "公司编辑", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:company_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 60, "menu_id": 60, "parent_id": 56, "type": 2, "title": "公司删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:company_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 61, "menu_id": 61, "parent_id": 57, "type": 2, "title": "部门新增", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:department_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 62, "menu_id": 62, "parent_id": 57, "type": 2, "title": "部门编辑", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:department_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 63, "menu_id": 63, "parent_id": 57, "type": 2, "title": "部门删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:department_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 64, "menu_id": 64, "parent_id": 13, "type": 2, "title": "一键下线", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:user_offline", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 65, "menu_id": 65, "parent_id": 0, "type": 1, "title": "设备管理", "icon": "icon-shebeiguan<PERSON>", "route": "/device", "path": "/src/layout/index.vue", "sort_num": 9, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 66, "menu_id": 66, "parent_id": 65, "type": 1, "title": "设备列表", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/device/list", "path": "/src/views/device/list/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 67, "menu_id": 67, "parent_id": 65, "type": 1, "title": "设备总览", "icon": "icon-<PERSON><PERSON><PERSON><PERSON>", "route": "/device/overview", "path": "/src/views/device/overview/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 68, "menu_id": 68, "parent_id": 65, "type": 1, "title": "新建测站", "icon": "icon-x<PERSON><PERSON><PERSON>", "route": "/device/addSurveyStation", "path": "/src/views/device/addSurveyStation/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 69, "menu_id": 69, "parent_id": 66, "type": 2, "title": "设备编辑", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:device_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 70, "menu_id": 70, "parent_id": 66, "type": 2, "title": "设备删除", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:device_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 71, "menu_id": 71, "parent_id": 66, "type": 2, "title": "设备设置", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:device_setup", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 72, "menu_id": 72, "parent_id": 66, "type": 2, "title": "设备重启", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:device_restart", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 73, "menu_id": 73, "parent_id": 66, "type": 2, "title": "设备调试", "icon": "", "route": "", "path": "", "sort_num": 5, "permission": "permission:device_debugger", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 74, "menu_id": 74, "parent_id": 66, "type": 2, "title": "设备详情", "icon": "", "route": "", "path": "", "sort_num": 6, "permission": "permission:device_detail", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 76, "menu_id": 76, "parent_id": 217, "type": 2, "title": "编辑基础信息", "icon": "", "route": "", "path": "", "sort_num": 7, "permission": "permission:surveyStation_baseEdit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 77, "menu_id": 77, "parent_id": 9, "type": 1, "title": "应用管理", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/backstage/application", "path": "/src/views/backstage/application/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 78, "menu_id": 78, "parent_id": 77, "type": 2, "title": "新增应用", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:application_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 79, "menu_id": 79, "parent_id": 77, "type": 2, "title": "删除应用", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:application_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 80, "menu_id": 80, "parent_id": 77, "type": 2, "title": "修改应用", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:application_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 84, "menu_id": 84, "parent_id": 92, "type": 1, "title": "实时降雨表", "icon": "", "route": "/statistics/rain/realTimeRainfall", "path": "/src/views/statistics/rain/realTimeRainfall/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 85, "menu_id": 85, "parent_id": 92, "type": 1, "title": "时段雨量", "icon": "", "route": "/statistics/rain/periodRainfall", "path": "/src/views/statistics/rain/periodRainfall/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 86, "menu_id": 86, "parent_id": 92, "type": 1, "title": "雨量极值表", "icon": "", "route": "/statistics/rain/rainfallExtremum", "path": "/src/views/statistics/rain/rainfallExtremum/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 87, "menu_id": 87, "parent_id": 92, "type": 1, "title": "历史同期对比", "icon": "", "route": "/statistics/rain/contemporaneousPeriod", "path": "/src/views/statistics/rain/contemporaneousPeriod/index.vue", "sort_num": 4, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 88, "menu_id": 88, "parent_id": 92, "type": 1, "title": "降雨日报表", "icon": "", "route": "/statistics/rain/daily", "path": "/src/views/statistics/rain/daily/index.vue", "sort_num": 5, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 89, "menu_id": 89, "parent_id": 92, "type": 1, "title": "降雨月报表", "icon": "", "route": "/statistics/rain/monthlyReport", "path": "/src/views/statistics/rain/monthlyReport/index.vue", "sort_num": 6, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 90, "menu_id": 90, "parent_id": 92, "type": 1, "title": "降雨旬报表", "icon": "", "route": "/statistics/rain/tenDayReport", "path": "/src/views/statistics/rain/tenDayReport/index.vue", "sort_num": 8, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 91, "menu_id": 91, "parent_id": 92, "type": 1, "title": "雨量单站对比", "icon": "", "route": "/statistics/rain/rainfallComparison", "path": "/src/views/statistics/rain/rainfallComparison/index.vue", "sort_num": 9, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 92, "menu_id": 92, "parent_id": 4, "type": 1, "title": "雨量统计报表", "icon": "icon-y<PERSON>qing", "route": "/statistics/rain", "path": "/src/layout/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 93, "menu_id": 93, "parent_id": 4, "type": 1, "title": "水情统计报表", "icon": "icon-gongshuitongjifenxi", "route": "/statistics/regimen", "path": "/src/layout/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 94, "menu_id": 94, "parent_id": 93, "type": 1, "title": "河道水情表", "icon": "", "route": "/statistics/regimen/riverSituation", "path": "/src/views/statistics/regimen/riverSituation/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 95, "menu_id": 95, "parent_id": 93, "type": 1, "title": "河道日报表", "icon": "", "route": "/statistics/regimen/riverDaily", "path": "/src/views/statistics/regimen/riverDaily/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 96, "menu_id": 96, "parent_id": 93, "type": 1, "title": "河道水情查询", "icon": "", "route": "/statistics/regimen/riverSituationQuery", "path": "/src/views/statistics/regimen/riverSituationQuery/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 97, "menu_id": 97, "parent_id": 93, "type": 1, "title": "河道单站对比", "icon": "", "route": "/statistics/regimen/riverContrast", "path": "/src/views/statistics/regimen/riverContrast/index.vue", "sort_num": 4, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 98, "menu_id": 98, "parent_id": 93, "type": 1, "title": "水库水情表", "icon": "", "route": "/statistics/regimen/reservoirCondition", "path": "/src/views/statistics/regimen/reservoirCondition/index.vue", "sort_num": 5, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 99, "menu_id": 99, "parent_id": 93, "type": 1, "title": "水库日报表", "icon": "", "route": "/statistics/regimen/reservoirDay", "path": "/src/views/statistics/regimen/reservoirDay/index.vue", "sort_num": 6, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 100, "menu_id": 100, "parent_id": 93, "type": 1, "title": "水库水情查询", "icon": "", "route": "/statistics/regimen/reservoirConditionsQuery", "path": "/src/views/statistics/regimen/reservoirConditionsQuery/index.vue", "sort_num": 7, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 101, "menu_id": 101, "parent_id": 93, "type": 1, "title": "水库单站对比", "icon": "", "route": "/statistics/regimen/reservoirComparison", "path": "/src/views/statistics/regimen/reservoirComparison/index.vue", "sort_num": 8, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 102, "menu_id": 102, "parent_id": 4, "type": 1, "title": "图像展示报表", "icon": "icon-ldc-investment", "route": "/statistics/imgReport", "path": "/src/layout/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 103, "menu_id": 103, "parent_id": 102, "type": 1, "title": "图像报表", "icon": "", "route": "/statistics/imgReport/img", "path": "/src/views/statistics/imgReport/img/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 104, "menu_id": 104, "parent_id": 65, "type": 1, "title": "故障管理", "icon": "icon-guzhangguzhangguanli", "route": "/device/hitch", "path": "/src/views/device/hitch/index.vue", "sort_num": 4, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 106, "menu_id": 106, "parent_id": 9, "type": 1, "title": "传感器管理", "icon": "", "route": "/backstage/sensor", "path": "/src/views/backstage/sensor/index.vue", "sort_num": 11, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 111, "menu_id": 111, "parent_id": 9, "type": 1, "title": "APP管理", "icon": "icon-appguanli", "route": "/backstage/apps", "path": "/src/views/backstage/apps/index.vue", "sort_num": 9, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 112, "menu_id": 112, "parent_id": 111, "type": 2, "title": "app添加", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:app_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 113, "menu_id": 113, "parent_id": 111, "type": 2, "title": "app编辑", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:app_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 114, "menu_id": 114, "parent_id": 111, "type": 2, "title": "app删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:app_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 115, "menu_id": 115, "parent_id": 65, "type": 1, "title": "设备日志", "icon": "icon-<PERSON><PERSON><PERSON><PERSON>", "route": "/device/device_log", "path": "/src/views/device/deviceLogs/index.vue", "sort_num": 10, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 116, "menu_id": 116, "parent_id": 66, "type": 2, "title": "设备损坏", "icon": "", "route": "", "path": "", "sort_num": 7, "permission": "permission:device_damage", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 117, "menu_id": 117, "parent_id": 66, "type": 2, "title": "历史参数", "icon": "", "route": "", "path": "", "sort_num": 10, "permission": "permission:device_config_list", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 122, "menu_id": 122, "parent_id": 5, "type": 1, "title": "原始报文", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/surveyStation/device_packet_log", "path": "/src/views/surveyStation/devicePacketLog/index.vue", "sort_num": 6, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 123, "menu_id": 123, "parent_id": 217, "type": 2, "title": "原始报文", "icon": "", "route": "", "path": "", "sort_num": 8, "permission": "permission:surveyStation_device_packeg_log", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 125, "menu_id": 125, "parent_id": 217, "type": 2, "title": "导出 Excel", "icon": "", "route": "", "path": "", "sort_num": 10, "permission": "permission:surveyStation_export", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 126, "menu_id": 126, "parent_id": 5, "type": 1, "title": "添加测站", "icon": "icon-tianjia", "route": "/surveyStation/add_new_station", "path": "/src//views/surveyStation/addNewStation/index.vue", "sort_num": 5, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 127, "menu_id": 127, "parent_id": 6, "type": 1, "title": "预警总览", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/earlyWarning/overview", "path": "/src/views/earlyWarning/overview/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 128, "menu_id": 128, "parent_id": 6, "type": 1, "title": "预警列表", "icon": "icon-dongtaiyujing", "route": "/earlyWarning/list", "path": "/src/views/earlyWarning/list/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 129, "menu_id": 129, "parent_id": 0, "type": 1, "title": "视频管理", "icon": "icon-video", "route": "/video", "path": "/src/layout/index.vue", "sort_num": 12, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 130, "menu_id": 130, "parent_id": 129, "type": 1, "title": "设备列表", "icon": "icon-ship<PERSON><PERSON><PERSON><PERSON>", "route": "/video/list", "path": "/src/view/video/list/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 131, "menu_id": 131, "parent_id": 129, "type": 1, "title": "视频播放", "icon": "icon-shipinbofang", "route": "/video/play", "path": "/src/views/video/play/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 132, "menu_id": 132, "parent_id": 131, "type": 2, "title": "视频控制", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:video_control", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 133, "menu_id": 133, "parent_id": 0, "type": 1, "title": "大屏管理", "icon": "icon-bigScreen", "route": "/bigScreen", "path": "/src/views/bigScreen/index.vue", "sort_num": 7, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 134, "menu_id": 134, "parent_id": 0, "type": 1, "title": "地图", "icon": "icon-home", "route": "/home", "path": "/src/views/home/<USER>", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 136, "menu_id": 136, "parent_id": 66, "type": 2, "title": "手动升级", "icon": "", "route": "", "path": "", "sort_num": 11, "permission": "permission:device_ota", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 137, "menu_id": 137, "parent_id": 9, "type": 1, "title": "驱动管理", "icon": "icon-qudongguanli", "route": "/backstage/driver", "path": "/src/views/backstage/driver/index.vue", "sort_num": 12, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 138, "menu_id": 138, "parent_id": 137, "type": 2, "title": "新增", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:driver_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 139, "menu_id": 139, "parent_id": 137, "type": 2, "title": "编辑", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:driver_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 140, "menu_id": 140, "parent_id": 137, "type": 2, "title": "删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:driver_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 141, "menu_id": 141, "parent_id": 137, "type": 2, "title": "传感器管理", "icon": "", "route": "", "path": "", "sort_num": 5, "permission": "permission:driver_add_sensor", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 143, "menu_id": 143, "parent_id": 129, "type": 1, "title": "分屏管理", "icon": "icon-a-8Dfenping", "route": "/video/live", "path": "/src/views/video/live/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 144, "menu_id": 144, "parent_id": 137, "type": 2, "title": "新增传感器", "icon": "", "route": "", "path": "", "sort_num": 6, "permission": "permission:sensor_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 145, "menu_id": 145, "parent_id": 137, "type": 2, "title": "编辑传感器", "icon": "", "route": "", "path": "", "sort_num": 7, "permission": "permission:sensor_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 146, "menu_id": 146, "parent_id": 137, "type": 2, "title": "删除传感器", "icon": "", "route": "", "path": "", "sort_num": 8, "permission": "permission:sensor_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 147, "menu_id": 147, "parent_id": 137, "type": 2, "title": "导出文件", "icon": "", "route": "", "path": "", "sort_num": 9, "permission": "permission:sensor_download", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 148, "menu_id": 148, "parent_id": 5, "type": 1, "title": "报文解析", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/surveyStation/packetDecode", "path": "/src/views/surveyStation/packetDecode/index.vue", "sort_num": 7, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 151, "menu_id": 151, "parent_id": 150, "type": 1, "title": "项目列表", "icon": "", "route": "/project/list", "path": "/src/views/project/list/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 158, "menu_id": 158, "parent_id": 0, "type": 1, "title": "项目管理", "icon": "icon-x<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/projectFrontend", "path": "/src/layout/project.vue", "sort_num": 11, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 159, "menu_id": 159, "parent_id": 158, "type": 1, "title": "项目总览", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/projectFrontend/home", "path": "/src/views/projectFrontend/home/<USER>", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 161, "menu_id": 161, "parent_id": 158, "type": 1, "title": "测站管理", "icon": "icon-ditudingwei", "route": "/projectFrontend/station", "path": "/src/views/projectFrontend/station/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 166, "menu_id": 166, "parent_id": 5, "type": 1, "title": "项目管理", "icon": "icon-x<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/surveyStation/project", "path": "/src/views/surveyStation/project/index.vue", "sort_num": 8, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 168, "menu_id": 168, "parent_id": 166, "type": 2, "title": "返回后台管理", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:back_to_manage", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 169, "menu_id": 169, "parent_id": 166, "type": 2, "title": "项目编辑", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:project_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 170, "menu_id": 170, "parent_id": 166, "type": 2, "title": "项目删除", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:project_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 172, "menu_id": 172, "parent_id": 158, "type": 2, "title": "项目切换", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:project_switch", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 175, "menu_id": 175, "parent_id": 158, "type": 1, "title": "测点管理", "icon": "icon-c<PERSON><PERSON><PERSON><PERSON>", "route": "/projectFrontend/datapoint", "path": "/src/layout/project.vue", "sort_num": 4, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 183, "menu_id": 183, "parent_id": 175, "type": 1, "title": "测点分布", "icon": "icon-ditudingwei", "route": "/projectFrontend/datapoint/distribution", "path": "/projectFrontend/datapoint/distribution/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 184, "menu_id": 184, "parent_id": 175, "type": 1, "title": "拓扑图", "icon": "icon-tuoputu", "route": "/projectFrontend/datapoint/topology", "path": "/projectFrontend/datapoint/topology/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 186, "menu_id": 186, "parent_id": 175, "type": 1, "title": "数据分析", "icon": "", "route": "/projectFrontend/datapoint/data-analysis", "path": "/projectFrontend/datapoint/dataAnalysis/index.vue", "sort_num": 4, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 187, "menu_id": 187, "parent_id": 175, "type": 1, "title": "组合分析", "icon": "icon-zuhefenxi", "route": "/projectFrontend/datapoint/combine-analysis", "path": "/projectFrontend/datapoint/combineAnalysis/index.vue", "sort_num": 5, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 188, "menu_id": 188, "parent_id": 175, "type": 1, "title": "特征值分析", "icon": "", "route": "/projectFrontend/datapoint/eigenvalue-analysis", "path": "/projectFrontend/datapoint/eigenvalueAnalysis/index.vue", "sort_num": 6, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 189, "menu_id": 189, "parent_id": 175, "type": 1, "title": "过程线分析", "icon": "", "route": "/projectFrontend/datapoint/process-analysis", "path": "/projectFrontend/datapoint/processAnalysis/index.vue", "sort_num": 7, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 190, "menu_id": 190, "parent_id": 175, "type": 1, "title": "相关性分析", "icon": "", "route": "/projectFrontend/datapoint/correlation-analysis", "path": "/projectFrontend/datapoint/correlationAnalysis/index.vue", "sort_num": 8, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 191, "menu_id": 191, "parent_id": 180, "type": 1, "title": "预警总览", "icon": "", "route": "/projectFrontend/earlyWarning/overview", "path": "/src/views/earlyWarning/overview/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 192, "menu_id": 192, "parent_id": 180, "type": 1, "title": "预警管理", "icon": "", "route": "/projectFrontend/earlyWarning/list", "path": "/src/views/earlyWarning/list/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 195, "menu_id": 195, "parent_id": 181, "type": 1, "title": "分屏管理", "icon": "", "route": "/projectFrontend/video/live", "path": "/src/views/video/live/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 196, "menu_id": 196, "parent_id": 181, "type": 1, "title": "视频列表", "icon": "", "route": "/projectFrontend/video/list", "path": "/src/view/video/list/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 197, "menu_id": 197, "parent_id": 181, "type": 1, "title": "视频播放", "icon": "", "route": "/projectFrontend/video/play", "path": "/src/views/video/play/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 198, "menu_id": 198, "parent_id": 160, "type": 1, "title": "巡视任务", "icon": "", "route": "/projectFrontend/visit/task", "path": "/src/views/projectFrontend/visit/task/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 199, "menu_id": 199, "parent_id": 160, "type": 1, "title": "巡视记录", "icon": "", "route": "/projectFrontend/visit/record", "path": "/src/views/projectFrontend/visit/record/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 202, "menu_id": 202, "parent_id": 175, "type": 1, "title": "浸润线", "icon": "icon-ji<PERSON><PERSON><PERSON><PERSON>", "route": "/projectFrontend/datapoint/phreatic-line", "path": "/src/views/projectFrontend/datapoint/phreaticLine/index.vue", "sort_num": 9, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 203, "menu_id": 203, "parent_id": 175, "type": 1, "title": "库容线", "icon": "icon-s<PERSON><PERSON><PERSON><PERSON>", "route": "/projectFrontend/datapoint/capacity-line", "path": "/src/views/projectFrontend/datapoint/capacityLine/index.vue", "sort_num": 10, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 205, "menu_id": 205, "parent_id": 175, "type": 1, "title": "水雨情", "icon": "icon-di<PERSON>osh<PERSON>hi-01", "route": "/projectFrontend/datapoint/water-and-rain", "path": "/src/views/projectFrontend/datapoint/waterAndRain/index.vue", "sort_num": 11, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 206, "menu_id": 206, "parent_id": 175, "type": 1, "title": "位移监测", "icon": "icon-weiyi<PERSON><PERSON>", "route": "/projectFrontend/datapoint/displacement-detection", "path": "/src/views/projectFrontend/datapoint/displacementDetection/index.vue", "sort_num": 12, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 207, "menu_id": 207, "parent_id": 175, "type": 1, "title": "渗压水位", "icon": "icon-shuiwei", "route": "/projectFrontend/datapoint/pressure-water", "path": "/src/views/projectFrontend/datapoint/pressureWater/index.vue", "sort_num": 13, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 209, "menu_id": 209, "parent_id": 217, "type": 2, "title": "添加测点", "icon": "", "route": "", "path": "", "sort_num": 12, "permission": "permission:datapoint_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 210, "menu_id": 210, "parent_id": 217, "type": 2, "title": "测点编辑", "icon": "", "route": "", "path": "", "sort_num": 13, "permission": "permission:datapoint_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 211, "menu_id": 211, "parent_id": 217, "type": 2, "title": "测点删除", "icon": "", "route": "", "path": "", "sort_num": 14, "permission": "permission:datapoint_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 213, "menu_id": 213, "parent_id": 0, "type": 1, "title": "水库管理", "icon": "icon-shuiku", "route": "/reservoir", "path": "/src/views/reservoir/index.vue", "sort_num": 6, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 215, "menu_id": 215, "parent_id": 5, "type": 1, "title": "基础数据", "icon": "icon-jurassic_form-basic", "route": "/surveyStation/baseData", "path": "/src/views/surveyStation/baseData/index.vue", "sort_num": 9, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 217, "menu_id": 217, "parent_id": 0, "type": 1, "title": "测站管理", "icon": "icon-surveyStation", "route": "/station", "path": "/src/views/station/index.vue", "sort_num": 5, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 219, "menu_id": 219, "parent_id": 65, "type": 1, "title": "物联网卡", "icon": "icon-liuliangkaguan<PERSON>", "route": "/device/flowCard", "path": "/src/views/device/flowCard/index.vue", "sort_num": 5, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 220, "menu_id": 220, "parent_id": 9, "type": 1, "title": "部署管理", "icon": "icon-bushug<PERSON><PERSON>", "route": "/backstage/deploy", "path": "/src/views/backstage/deploy/index.vue", "sort_num": 5, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 230, "menu_id": 230, "parent_id": 0, "type": 1, "title": "控制台", "icon": "icon-caida<PERSON><PERSON><PERSON>", "route": "/control", "path": "/src/layout/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 235, "menu_id": 235, "parent_id": 0, "type": 1, "title": "巡视管理", "icon": "icon-x<PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/projectVisit", "path": "/src/layout/index.vue", "sort_num": 15, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 244, "menu_id": 244, "parent_id": 235, "type": 1, "title": "巡视任务", "icon": "icon-x<PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/projectVisit/task", "path": "/src/views/projectFrontend/visit/task/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 245, "menu_id": 245, "parent_id": 235, "type": 1, "title": "巡视记录", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/projectVisit/record", "path": "/src/views/projectFrontend/visit/record/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 247, "menu_id": 247, "parent_id": 0, "type": 1, "title": "公共权限", "icon": "", "route": "/common", "path": "/src/layout/index.vue", "sort_num": 17, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 248, "menu_id": 248, "parent_id": 247, "type": 2, "title": "公司切换", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:company_switch", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 250, "menu_id": 250, "parent_id": 215, "type": 2, "title": "同步数据", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:basedata_sync", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 251, "menu_id": 251, "parent_id": 215, "type": 2, "title": "新增", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:basedata_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 252, "menu_id": 252, "parent_id": 215, "type": 2, "title": "数据导入", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:basedata_import", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 253, "menu_id": 253, "parent_id": 215, "type": 2, "title": "数据导出", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:basedata_export", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 254, "menu_id": 254, "parent_id": 215, "type": 2, "title": "编辑", "icon": "", "route": "", "path": "", "sort_num": 5, "permission": "permission:basedata_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 255, "menu_id": 255, "parent_id": 215, "type": 2, "title": "删除", "icon": "", "route": "", "path": "", "sort_num": 6, "permission": "permission:basedata_delete", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 256, "menu_id": 256, "parent_id": 126, "type": 2, "title": "添加新测站", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:add_new_station", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 257, "menu_id": 257, "parent_id": 122, "type": 2, "title": "查看内容详情", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:view_device_packet_log", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 258, "menu_id": 258, "parent_id": 148, "type": 2, "title": "报文解析", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:packet_decode", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 259, "menu_id": 259, "parent_id": 301, "type": 2, "title": "项目档案添加", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:project_archives_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 260, "menu_id": 260, "parent_id": 301, "type": 2, "title": "项目档案下载", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:project_archives_download", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 261, "menu_id": 261, "parent_id": 301, "type": 2, "title": "项目档案删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:project_archives_delete", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 263, "menu_id": 263, "parent_id": 130, "type": 2, "title": "添加视频", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:video_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 264, "menu_id": 264, "parent_id": 130, "type": 2, "title": "查看视频", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:video_view", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 265, "menu_id": 265, "parent_id": 130, "type": 2, "title": "视频编辑", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:video_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 266, "menu_id": 266, "parent_id": 130, "type": 2, "title": "视频删除", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:video_delete", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 267, "menu_id": 267, "parent_id": 130, "type": 2, "title": "查看配置", "icon": "", "route": "", "path": "", "sort_num": 5, "permission": "permission:video_view_config", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 268, "menu_id": 268, "parent_id": 217, "type": 2, "title": "添加预警阈值", "icon": "", "route": "", "path": "", "sort_num": 15, "permission": "permission:alarm_threshold_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 269, "menu_id": 269, "parent_id": 217, "type": 2, "title": "预警阈值编辑", "icon": "", "route": "", "path": "", "sort_num": 16, "permission": "permission:alarm_threshold_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 270, "menu_id": 270, "parent_id": 217, "type": 2, "title": "预警阈值删除", "icon": "", "route": "", "path": "", "sort_num": 17, "permission": "permission:alarm_threshold_delete", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 271, "menu_id": 271, "parent_id": 217, "type": 2, "title": "测站读取配置", "icon": "", "route": "", "path": "", "sort_num": 18, "permission": "permission:station_read_config", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 272, "menu_id": 272, "parent_id": 217, "type": 2, "title": "测站设置配置", "icon": "", "route": "", "path": "", "sort_num": 19, "permission": "permission:station_set_config", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 273, "menu_id": 273, "parent_id": 166, "type": 2, "title": "项目添加", "icon": "", "route": "", "path": "", "sort_num": 5, "permission": "permission:project_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 274, "menu_id": 274, "parent_id": 301, "type": 2, "title": "位置管理添加", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:location_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 275, "menu_id": 275, "parent_id": 301, "type": 2, "title": "位置管理编辑", "icon": "", "route": "", "path": "", "sort_num": 5, "permission": "permission:location_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 276, "menu_id": 276, "parent_id": 301, "type": 2, "title": "位置管理删除", "icon": "", "route": "", "path": "", "sort_num": 6, "permission": "permission:location_delete", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 277, "menu_id": 277, "parent_id": 301, "type": 2, "title": "大坝断面添加", "icon": "", "route": "", "path": "", "sort_num": 7, "permission": "permission:section_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 278, "menu_id": 278, "parent_id": 301, "type": 2, "title": "大坝断面编辑", "icon": "", "route": "", "path": "", "sort_num": 8, "permission": "permission:section_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 279, "menu_id": 279, "parent_id": 301, "type": 2, "title": "大坝断面删除", "icon": "", "route": "", "path": "", "sort_num": 9, "permission": "permission:section_delete", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 281, "menu_id": 281, "parent_id": 158, "type": 1, "title": "数据服务", "icon": "icon-jurassic_form-basic", "route": "/projectFrontend/dataService", "path": "/src/layout/index.vue", "sort_num": 5, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 282, "menu_id": 282, "parent_id": 158, "type": 1, "title": "评估分析", "icon": "icon-statistics", "route": "/projectFrontend/analysis", "path": "/src/views/projectFrontend/analysis/index.vue", "sort_num": 6, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 283, "menu_id": 283, "parent_id": 281, "type": 1, "title": "数据查询", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "route": "/projectFrontend/dataService/check", "path": "/projectFrontend/dataService/check/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 284, "menu_id": 284, "parent_id": 281, "type": 1, "title": "报表下载", "icon": "icon-baobiaoxiazai1", "route": "/projectFrontend/dataService/download", "path": "/projectFrontend/dataService/download/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 285, "menu_id": 285, "parent_id": 281, "type": 1, "title": "报表配置", "icon": "icon-baobiaopeizhi2", "route": "/projectFrontend/dataService/reportConfig", "path": "/projectFrontend/dataService/reportConfig/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 286, "menu_id": 286, "parent_id": 281, "type": 1, "title": "数据计算", "icon": "", "route": "/projectFrontend/dataService/clac", "path": "/projectFrontend/dataService/calc/index.vue", "sort_num": 4, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 2, "desc": "", "checked": 0, "children": null}, {"id": 287, "menu_id": 287, "parent_id": 282, "type": 1, "title": "数据对比", "icon": "icon-shujul<PERSON><PERSON><PERSON>iao-zhuanqu_fuzhi-", "route": "/projectFrontend/analysis/comparison", "path": "/projectFrontend/analysis/comparison/index.vue", "sort_num": 1, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 288, "menu_id": 288, "parent_id": 282, "type": 1, "title": "数据关联", "icon": "icon-shujuguanlian", "route": "/projectFrontend/analysis/association", "path": "/src/views/projectFrontend/analysis/association/index.vue", "sort_num": 2, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 290, "menu_id": 290, "parent_id": 9, "type": 1, "title": "定时任务", "icon": "icon-zuhefenxi", "route": "/backstage/crontask", "path": "/src/views/backstage/crontask/index.vue", "sort_num": 13, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 292, "menu_id": 292, "parent_id": 290, "type": 2, "title": "定时任务删除", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:crontask_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 294, "menu_id": 294, "parent_id": 284, "type": 2, "title": "下载文件", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:dataservice_download", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 295, "menu_id": 295, "parent_id": 284, "type": 2, "title": "删除文件", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:dataservice_delete", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 297, "menu_id": 297, "parent_id": 284, "type": 2, "title": "上传", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:dataservice_upload", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 301, "menu_id": 301, "parent_id": 158, "type": 1, "title": "项目详情", "icon": "icon-xiang<PERSON>", "route": "/projectFrontend/projectDetail", "path": "/src/views/projectFrontend/projectDetail/index.vue", "sort_num": 3, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 303, "menu_id": 303, "parent_id": 284, "type": 2, "title": "创建文件夹", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:dataservice_createdir", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 305, "menu_id": 305, "parent_id": 0, "type": 1, "title": "GIS页面", "icon": "icon-WebGIS", "route": "/webgis", "path": "/src/views/webgis/index.vue", "sort_num": 18, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 307, "menu_id": 307, "parent_id": 0, "type": 1, "title": "站点统计", "icon": "icon-zuhefenxi", "route": "/chartstatues", "path": "/src/views/chartstatues/index.vue", "sort_num": 19, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 309, "menu_id": 309, "parent_id": 215, "type": 2, "title": "导入数据rtuv2m8", "icon": "", "route": "", "path": "", "sort_num": 7, "permission": "permission:base_data_rtuv2m8", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 310, "menu_id": 310, "parent_id": 215, "type": 2, "title": "导入数据 SL2022", "icon": "", "route": "", "path": "", "sort_num": 8, "permission": "permission:base_data_sl2022", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 312, "menu_id": 312, "parent_id": 307, "type": 2, "title": "下面的图数据导出", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:chartstatus_count_export", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 313, "menu_id": 313, "parent_id": 307, "type": 2, "title": "表格数据导出", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:chartstatus_table_export", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 315, "menu_id": 315, "parent_id": 301, "type": 2, "title": "2D测点布设", "icon": "", "route": "", "path": "", "sort_num": 10, "permission": "permission:add_point_2d", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 316, "menu_id": 316, "parent_id": 301, "type": 2, "title": "3D测点布设", "icon": "", "route": "", "path": "", "sort_num": 11, "permission": "permission:set_point_3d", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 318, "menu_id": 317, "parent_id": 215, "type": 2, "title": "导入数据 SL651", "icon": "", "route": "", "path": "", "sort_num": 9, "permission": "permission:base_data_sl651", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 320, "menu_id": 318, "parent_id": 9, "type": 1, "title": "承建商管理", "icon": "icon-c<PERSON><PERSON><PERSON><PERSON>", "route": "/backstage/contractor", "path": "/src/views/backstage/contractor/index.vue", "sort_num": 14, "permission": "", "is_keepalive": 2, "is_visible": 1, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 322, "menu_id": 319, "parent_id": 318, "type": 2, "title": "新增", "icon": "", "route": "", "path": "", "sort_num": 1, "permission": "permission:contractor_add", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 323, "menu_id": 320, "parent_id": 318, "type": 2, "title": "编辑", "icon": "", "route": "", "path": "", "sort_num": 2, "permission": "permission:contractor_edit", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 324, "menu_id": 321, "parent_id": 318, "type": 2, "title": "删除", "icon": "", "route": "", "path": "", "sort_num": 3, "permission": "permission:contractor_del", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 326, "menu_id": 322, "parent_id": 217, "type": 2, "title": "故障处理", "icon": "", "route": "", "path": "", "sort_num": 20, "permission": "permission:handle_fault", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 328, "menu_id": 323, "parent_id": 15, "type": 2, "title": "菜单导出", "icon": "", "route": "", "path": "", "sort_num": 4, "permission": "permission:menu_export", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 329, "menu_id": 324, "parent_id": 15, "type": 2, "title": "菜单导入", "icon": "", "route": "", "path": "", "sort_num": 5, "permission": "permission:menu_import", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}, {"id": 331, "menu_id": 325, "parent_id": 215, "type": 2, "title": "删除模板数据", "icon": "", "route": "", "path": "", "sort_num": 10, "permission": "permission:basedata_truncate", "is_keepalive": 0, "is_visible": 0, "status": 1, "desc": "", "checked": 0, "children": null}]