[{"id": 22, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-05-10T00:01:31.173+08:00", "driver_id": 9, "sn": 51, "name": "工业相机", "status": true, "params": {}, "desc": "工业相机", "protoc_file_url": ""}, {"id": 17, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-05-10T00:01:31.174+08:00", "driver_id": 5, "sn": 61, "name": "雨量计", "status": true, "params": {}, "desc": "雨量计", "protoc_file_url": ""}, {"id": 27, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-08-14T11:11:22.233605+08:00", "driver_id": 7, "sn": 3, "name": "禾信-雷达水位计HXCAR820K", "status": true, "params": {"cmd_read": 3, "protocol_type": 1, "reg_addr": 1, "reg_size": 1, "reg_squ": 3, "type": 57}, "desc": "测量结果是空高，单位是 mm，所以参数 a = - 0.001， 参数 b = 水位基值 + 水位修正值", "protoc_file_url": ""}, {"id": 28, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-08-14T11:11:25.273772+08:00", "driver_id": 7, "sn": 4, "name": "南水-浮子式水位计WFH-2D", "status": true, "params": {"cmd_read": 3, "protocol_type": 1, "reg_addr": 0, "reg_size": 1, "reg_squ": 1, "type": 57}, "desc": "读取数据的单位是 cm ，所以参数  a = 0.01，参数b = 水位基值 + 水位修正值", "protoc_file_url": ""}, {"id": 4, "created_at": "2024-05-10T18:26:32.793795+08:00", "updated_at": "2024-05-13T18:16:43.022269+08:00", "driver_id": 7, "sn": 1, "name": "点击选择", "status": true, "params": {"cmd_read": 3, "reg_addr": 0, "reg_size": 1, "reg_squ": 3, "type": 2}, "desc": "占位用，编号 1，是指专家模式，手动配置全部参数", "protoc_file_url": ""}, {"id": 37, "created_at": "2024-08-14T11:49:14.053639+08:00", "updated_at": "2024-08-14T16:36:11.93698+08:00", "driver_id": 7, "sn": 11, "name": "麦克文本协议", "status": true, "params": {"cmd_read": 0, "protocol_type": 2, "reg_addr": 0, "reg_size": 0, "reg_squ": 0, "type": 57}, "desc": "只配置 RS485相关接口", "protoc_file_url": ""}, {"id": 29, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-08-14T11:11:29.149431+08:00", "driver_id": 7, "sn": 5, "name": "星仪传感器-投入式水位计CYW11", "status": true, "params": {"cmd_read": 3, "protocol_type": 1, "reg_addr": 0, "reg_size": 1, "reg_squ": 3, "type": 57}, "desc": "传感器 0 ～ 2000 对应的是深度是 0 ～ 5m，所以参数 a = 0.0025 ，参数 b = 水位基值 + 水位修正值", "protoc_file_url": ""}, {"id": 30, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-08-14T11:11:32.333109+08:00", "driver_id": 7, "sn": 6, "name": "佳佳眼-雷达水位计NC-LLD3", "status": true, "params": {"cmd_read": 4, "protocol_type": 1, "reg_addr": 2575, "reg_size": 2, "reg_squ": 18, "type": 57}, "desc": "默认R485的地址是 127\n读取结果单位是 m , 是空高， 参数 a =-1,  参数 b = 水位基值 + 水位修正值", "protoc_file_url": ""}, {"id": 39, "created_at": "2024-09-12T15:54:49.013127+08:00", "updated_at": "2024-09-12T15:54:49.013127+08:00", "driver_id": 8, "sn": 1, "name": "渗压计", "status": true, "params": {}, "desc": "原始数据上报，无参数", "protoc_file_url": ""}, {"id": 31, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-08-14T11:11:35.55974+08:00", "driver_id": 7, "sn": 7, "name": "麦克-水位计-MPM47XX", "status": true, "params": {"cmd_read": 4, "protocol_type": 1, "reg_addr": 36, "reg_size": 2, "reg_squ": 18, "type": 57}, "desc": "读取结果单位是 m，所以参数 a = 1，参数 b = 水位基值 + 水位修正值", "protoc_file_url": ""}, {"id": 32, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-08-14T11:11:44.976333+08:00", "driver_id": 7, "sn": 8, "name": "海川博通-气泡式水位计", "status": true, "params": {"cmd_read": 4, "protocol_type": 1, "reg_addr": 36, "reg_size": 2, "reg_squ": 18, "type": 57}, "desc": "读取结果单位是 m，所以参数 a = 1，参数 b = 水位基值 + 水位修正值", "protoc_file_url": ""}, {"id": 33, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-08-14T11:11:47.710452+08:00", "driver_id": 7, "sn": 9, "name": "科浩-气泡式水位计KH.WQX-1", "status": true, "params": {"cmd_read": 3, "protocol_type": 1, "reg_addr": 0, "reg_size": 1, "reg_squ": 3, "type": 57}, "desc": "读取结果单位是 mm，所以参数 a = 0.001，参数 b = 水位基值 + 水位修正值", "protoc_file_url": ""}, {"id": 5, "created_at": "2024-05-22T12:02:21.707507+08:00", "updated_at": "2024-08-14T11:11:50.192082+08:00", "driver_id": 7, "sn": 10, "name": "南京润息科技雷达水位计", "status": true, "params": {"cmd_read": 3, "protocol_type": 1, "reg_addr": 1, "reg_size": 1, "reg_squ": 3, "type": 57}, "desc": "南京润息科技雷达水位计，默认地址 1，读取数据单位 mm，参数 a =-0.001", "protoc_file_url": ""}, {"id": 26, "created_at": "0001-01-01T00:00:00Z", "updated_at": "2024-08-14T11:11:11.415674+08:00", "driver_id": 7, "sn": 2, "name": "伟思-格雷码浮子水位计", "status": true, "params": {"cmd_read": 3, "protocol_type": 1, "reg_addr": 0, "reg_size": 1, "reg_squ": 3, "type": 57}, "desc": "读取结果的单位是 cm，所以参数 a = 0.01，参数 b = 水位基值 + 水位修正值", "protoc_file_url": ""}]