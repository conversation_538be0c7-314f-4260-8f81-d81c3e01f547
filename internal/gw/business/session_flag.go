package business

// session 中缓存的 station 运行时信息
// 为后面做缺项、缺报做准备package business

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	"bs.com/ebox/global"
	"bs.com/ebox/internal/gw/dto"
	"bs.com/ebox/internal/gw/model"
	"bs.com/ebox/pkg/bean"
	"bs.com/ebox/pkg/sl2022"
	"bs.com/ebox/pkg/sl651"
	"bs.com/ebox/pkg/timewheel"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xutils"
)

// 在 station 中添加一个 flag的概念，具体请参考 StationFlag，包括如下状态：
// 失联：超过 12 个小时没有收到任何报文，则认为 station 失联。
// 缺报：正常情况下，这个测站会上报心跳、小时报、图像报。如果没有上报小时报，则状态改为缺报。
// 缺项：每一个报文都要上报一些数据（对应采集要素的数据，采集要素的标识在 station.meta.elements 中）。如果缺少数据项，则为缺项。
// 正常：不缺项，小时报正常。

// 四个状态不冲突。

// 时间轮：如何使用时间轮？来缺报定时检查 flag。缺报和失联是需要用时间轮来判断的

type sessionManager struct {
	stationRuntime sync.Map // key: station_sn, value: *stationRuntime

	// 用于检查小时报缺报
	hourlyWheel     *timewheel.TimeWheel
	hourlyTimeoutCh chan *timewheel.NodeInfo

	// 用于检查失联
	lostWheel     *timewheel.TimeWheel
	lostTimeoutCh chan *timewheel.NodeInfo

	dao model.IDaoStation

	ctx    context.Context
	cancel context.CancelFunc
}

// 注意时间轮的精度是 60 秒。会有一些误差
func NewSessionManager(dao model.IDaoStation) *sessionManager {
	hourlyTimeoutCh := make(chan *timewheel.NodeInfo, 100)
	lostTimeoutCh := make(chan *timewheel.NodeInfo, 100)
	sm := &sessionManager{
		dao:             dao,
		stationRuntime:  sync.Map{},
		hourlyTimeoutCh: hourlyTimeoutCh,
		hourlyWheel:     timewheel.NewTimeWheel("hourly-check", 60, 60, hourlyTimeoutCh), // 每分钟检查一次，一圈60分钟

		lostTimeoutCh: lostTimeoutCh,
		lostWheel:     timewheel.NewTimeWheel("lost-check", 60, 120, lostTimeoutCh), // 每分钟检查一次，一圈 120分钟
	}
	return sm
}

func (sm *sessionManager) start(ctx context.Context) {
	sm.ctx, sm.cancel = context.WithCancel(ctx)

	defer sm.cancel() // 退出时，关闭子线程

	// 启动两个时间轮
	go sm.hourlyWheel.Start(ctx)
	go sm.lostWheel.Start(ctx)

	// 时间轮超时处理
	go sm.handleHourlyTimeout(ctx)

	go sm.handleLostTimeout(ctx)

	// 开机不去遍历历史记录，简化
	// sm.initStationRuntime() //通过历史报文记录，初始化 station Runtime

	<-ctx.Done()
}

const (
	LostTimeoutSeconds   int64 = 12 * 3600 // 失联超时时间（秒）
	HourlyTimeoutSeconds int64 = 3 * 3600  // 小时报超时时间（秒）
)

// 找到最近收到的 5 个报文，重放报文
func (sm *sessionManager) getLatestPacketData(ssn string) []*model.PacketData {
	result := []*model.PacketData{}
	// 查询最新的 4条小时报
	pktDataArr, err := sm.dao.GetLatestPacketsData(ssn, sl2022.FN34, 2)
	if err != nil {
		xlog.Warn("get lastet raw packet log failed", "ssn", ssn, "err", err.Error())
	} else {
		result = append(result, pktDataArr...)
	}

	// 查询最新的 1 条图像报
	pdE4, err := sm.dao.GetLatestPacketData(ssn, sl2022.FNE4)
	if err != nil {
		// 没有图像报文
	} else {
		result = append(result, pdE4)
	}

	return result
}

// 从 packet_data 表中加载数据，初始化 flag
func (sm *sessionManager) initStationRuntime() {
	defer xutils.Recovery("station runtime init")

	xlog.Debug("station session manager : init")
	// 每次重启系统，就需要加载所有测站的最近一条消息状态到缓存，初始化为失联状态。（最近一条消息未必够）

	ssnArr, err := sm.dao.ListAllStationSN("") //列出全部测站，不支持水资源
	if err != nil {
		xlog.Warn("list all station_sn failed", "err", err.Error())
		return
	}

	// 遍历来加载数据到缓存  FN34 FNE4 等
	for _, ssn := range ssnArr {
		pktDataArr := sm.getLatestPacketData(ssn)
		if len(pktDataArr) == 0 {
			continue
		}

		// 1、解析这些历史报文，并判断下是不是缺项。
		// 因为查询的时候用的是 id DESC
		for i := len(pktDataArr) - 1; i >= 0; i-- {
			pktData := pktDataArr[i]
			xlog.Debug("sl2022 session manager: check packet data", "ssn", ssn, "ts", pktData.Timestamp)

			// 之前的历史数据使用 F0,但是图像报文没有 F0
			if pktData.Timestamp == 0 {
				pktData.Timestamp = pktData.CreatedAt.Unix()
			}

			// 处理 pktData
			msg := &bean.ReportMessage{
				StationSN: ssn,
				Protocal:  pktData.Protocal,
				FN:        int64(pktData.Fn),
				Timestamp: pktData.Timestamp,
				Data:      strMapToUintMap(pktData.Data),
			}

			// 解析报文

			stOne, _ := sm.dao.StationGetByStationSN(msg.StationSN)
			if stOne == nil {
				xlog.Error("sl651 report: invalid station", "ssn", msg.StationSN)
				continue
			}

			once := &onceReport{
				curStation: stOne,
				ILogger:    xlog.WithSSN(msg.StationSN),
				Protocal:   msg.Protocal,
				StationSN:  msg.StationSN,
				FN:         uint8(msg.FN),
				Timestamp:  endTime5minute(msg.Timestamp, 1),
			}

			xlog.WithSSN(ssn).Debug("             check packet data", "msg", msg)
			if once.Protocal == global.P_SL2022 {
				once.sl2022Report(msg)
			} else if once.Protocal == global.P_SL651 {
				once.sl651Report(msg)
			}

			// 更新到 session
			_ = sm.OnSessionReport(once)
		}

		// 构建 rumtime，同时检查此测站是否要做历史数据检查
		runtime, err := sm.getRuntime(ssn)
		if err != nil {
			xlog.Error("get runtime failed", "err", err)
			continue
		}

		// 2、分析完历史数据，再判断当前是否缺报、失联。
		tsNow := time.Now().Unix()

		if runtime.LastMessageAt == 0 {
			runtime.Flag = dto.StationFlagUnknown
		} else {
			ts34 := runtime.GetLastMessage(sl2022.FN34)

			// 失联：已经 12个小时没有新消息了
			if tsNow-runtime.LastMessageAt > (LostTimeoutSeconds - 60) {
				runtime.Flag = dto.StationFlagLost
			} else if tsNow-ts34 > (HourlyTimeoutSeconds - 60) {
				// 缺报：缓存中没有找到小时报，或者超过 3 个小时没有小时报
				xlog.Debug("station flag QB -------------------------------- 2", "sn", ssn, "ts34", ts34)
				runtime.Flag = dto.StationFlagQB
			} else {
				runtime.Flag = dto.StationFlagOk
			}
		}

		// 重放数据之后，如果是 OK，再检查每一个 LastReportFmt，看是否缺项
		if runtime.Flag == dto.StationFlagOk {
			runtime.RangeLastReportFmt(func(k string, one *dto.EleDetail) bool {
				if one.Value == nil {
					runtime.Flag = dto.StationFlagQX
					return false
				}
				if tsNow-one.Timestamp > (HourlyTimeoutSeconds - 60) {
					runtime.Flag = dto.StationFlagQX
					return false
				}
				return true
			})
		}

		// 更新
		sm.stationRuntime.Store(ssn, runtime)
		xlog.Debug("============== sl2022 set station flag", "ssn", ssn, "flag", dto.StationFlagName(runtime.Flag))
		sm.dao.SetStationFlag(ssn, runtime.Flag)
		continue

	}

	// 调试：遍历一下缓存
	sm.stationRuntime.Range(func(key, value interface{}) bool {
		// key 和 value 都是 interface{} 类型，需要类型断言
		xlog.Debug("check station status", "ssn", key.(string), "flag", value.(*dto.StationRuntime).Flag)
		return true
	})

}

// 初始化运行时数据
func (sm *sessionManager) getRuntime(stationSN string) (*dto.StationRuntime, error) {
	var runtime *dto.StationRuntime

	valRt, ok1 := sm.stationRuntime.Load(stationSN)
	if ok1 {
		var ok2 bool
		runtime, ok2 = valRt.(*dto.StationRuntime)
		if !ok2 {
			xlog.Error("get runtime...... assert failed")
		}

		// 限制一个小时更新一次 runtime
		if time.Since(runtime.UpdatedAt) < time.Hour*1 {
			return runtime, nil // 如果这里直接返回了，后面更新了 station 的 elements，就无法同步了
		}
	} else {
		xlog.Warn("station runtime do first initialized", "sn", stationSN)
		runtime = &dto.StationRuntime{
			StationSN:     stationSN,
			Flag:          dto.StationFlagUnknown, //初始化：未知状态
			LastReportFmt: make(map[string]*dto.EleDetail, 0),
			LastMessage:   make(map[uint8]int64, 0),
		}
	}

	runtime.UpdatedAt = time.Now()
	defer sm.stationRuntime.Store(stationSN, runtime) // 每次读取 runtime，都有手动存储

	// 每次更新：可以缓存优化
	// 1、查询此测站信息
	station, err := sm.dao.StationGetByStationSN(stationSN)
	if err != nil {
		xlog.Errorf("get stationsn:%s station error: %v", stationSN, err)
		return nil, err
	}

	// 初始化 LastReportFmt
	eleArr := []string{}

	if station.Protocal == global.P_SL2022 {
		// 2、对于 sl2022 协议，直接取测站的 meta.elements
		val, ok := station.Meta["elements"]
		if !ok {
			xlog.Error("station meta elements not exist", "ssn", stationSN)
			return nil, errors.New("no elements: do not need check flag")
		}

		elements, ok := val.(string)
		if !ok || elements == "" {
			xlog.Error("station meta elements not string", "ssn", stationSN)
			return nil, errors.New("invalid elements: do not need check flag")
		}

		runtime.Elements = elements
		eleArr = strings.Split(runtime.Elements, ",")
	} else if station.Protocal == global.P_SL651 {
		//3、常规的 SL651，以及相关扩展
		// 从测点表里面查询，然后 pointType 对应 TemplateElementSl2022 转换到 Elements 中存储
		// 查询此测站的所有测点列表
		dataPointList, _ := sm.dao.DataPointListBySSN(station.StationSN)
		if len(dataPointList) == 0 {
			xlog.Error("station has no datapoint", "ssn", station.StationSN)
			return nil, errors.New("no datapoints: do not need check flag")
		}

		// 可按需扩展
		for _, one := range dataPointList {
			switch one.PointType {
			case "rain":
				eleArr = append(eleArr, "1", "9")
			case "image":
				eleArr = append(eleArr, "2")
			case "water":
				eleArr = append(eleArr, "8")

			// "20": {"表面位移", "displace"},
			// "21": {"GNSS表面位移", "gnss"},
			case "displace":
				eleArr = append(eleArr, "20")

			case "gnss":
				eleArr = append(eleArr, "21")

			// "22": {"渗压计", "ff_vwp_water"}, //带原始值和渗压水位
			// "23": {"渗压水位", "vwp_water"},   //仅渗压水位
			case "ff_vwp_water":
				eleArr = append(eleArr, "22")
			case "vwp_water":
				eleArr = append(eleArr, "23")

			default:
				xlog.Warn("站点统计，暂不支持 SL651 协议的其他测点类型：" + one.PointType)
			}
		}

		runtime.Elements = strings.Join(eleArr, ",")
	} else {
		return nil, errors.New("not support protocal : " + station.Protocal)
	}

	//4、填充 header 信息
	for _, ele := range eleArr {
		// 最新数据更新到 LastReportFmt ，方面前端格式化显示用
		_, ok := runtime.GetLastReportFmt(ele)
		if !ok {
			header, ok := dto.GetTypeHeaders(model.TemplateElementSl2022[ele].PointType)

			if !ok {
				// 此处，雨量整编和水位整编没有 header
				// 	"8":  {"水位整编", "hour_water"}, // 小时报水位整编
				// 	"9":  {"降水整编", "hour_rain"},  // 小时报雨量整编

				if ele == "8" {
					header = map[string]string{
						"timestamp": "时间",
						"value":     "水位整编(m)",
					}
				}
				if ele == "9" {
					header = map[string]string{
						"timestamp": "时间",
						"value":     "雨量整编(mm)",
					}
				}
			}

			// 初始化都是无数据
			runtime.SetLastReportFmt(ele, &dto.EleDetail{
				EleNum:    ele,
				Name:      model.TemplateElementSl2022[ele].Name,
				Status:    dto.EleStatusNOTOK,
				Header:    header,
				Value:     nil, // 默认没数据
				Timestamp: 0,
			})
		}
	}

	return runtime, nil
}

// 当时间轮超时，检查是失联还是缺报
func (sm *sessionManager) handleHourlyTimeout(ctx context.Context) {
	defer xutils.Recovery("station hourly timeout")

	for {
		select {
		case <-ctx.Done():
			return
		case info := <-sm.hourlyTimeoutCh: //某个设备心跳超时
			xlog.Debug("station hourly timeout", "sn", info.NID)
			// 和 tcp server 不同，我们只需要判断 stationSN 这个 session 是否超时
			stationSN := info.NID

			runtime, err := sm.getRuntime(stationSN)
			if err != nil {
				xlog.Warn("do not handle hourly timeout", "stationSN", stationSN)
				continue
			}

			// 超时说明缺报
			runtime.Flag = dto.StationFlagQB

			// 更新到缓存
			xlog.Debug("============== sl2022 set station flag : hourly timeout", "ssn", stationSN, "flag", dto.StationFlagName(runtime.Flag))
			sm.stationRuntime.Store(stationSN, runtime)
			sm.dao.SetStationFlag(stationSN, runtime.Flag)
		}
	}
}

func (sm *sessionManager) handleLostTimeout(ctx context.Context) {
	defer xutils.Recovery("station lost timeout")

	for {
		select {
		case <-ctx.Done():
			return

		case info := <-sm.lostTimeoutCh:
			xlog.Debug("station lost timeout", "sn", info.NID)
			// 和 tcp server 不同，我们只需要判断 stationSN 这个 session 是否超时
			stationSN := info.NID

			runtime, err := sm.getRuntime(stationSN)
			if err != nil {
				xlog.Warn("do not handle lost timeout", "stationSN", stationSN)
				continue
			}

			// 超时说明失联
			runtime.Flag = dto.StationFlagLost
			// 更新缓存
			xlog.Debug("============== sl2022 set station flag: lost timeout", "ssn", stationSN, "flag", dto.StationFlagName(runtime.Flag))
			sm.stationRuntime.Store(stationSN, runtime)
			sm.dao.SetStationFlag(stationSN, runtime.Flag)
		}
	}
}

// 更新时间轮：重启的时候，需要修正超时时间
func (sm *sessionManager) doHeartbeat(once *onceReport) {
	stationSN := once.StationSN

	// 收到任何消息都重置失联检查
	lostSeconds := LostTimeoutSeconds - (time.Now().Unix() - once.Timestamp)
	if lostSeconds > 0 {
		sm.lostWheel.Heartbeat(stationSN, "", int(lostSeconds))
	}

	// 如果是小时报
	if once.FN == sl2022.FN34 {
		hourlySeconds := HourlyTimeoutSeconds - (time.Now().Unix() - once.Timestamp)
		if hourlySeconds > 0 {
			sm.hourlyWheel.Heartbeat(stationSN, "", int(hourlySeconds))
		}
	}

}

// TODO:支持其他 protocal

// 每次收到消息：
// 判断缺项：需要针对不同的 FN 做不同的处理
// 小时报，就检查 1 ～ 14个要素中，是否漏掉了哪个。不考虑图像这个要素。
// 图像报：处理图像报的状态（之前缺项的，要判断 flag）。

func (sm *sessionManager) OnSessionReport(once *onceReport) error {
	// 时间轮更新
	sm.doHeartbeat(once)

	stationSN := once.StationSN
	// xlog.Debug("station session on report .... 1", "ssn", stationSN, "fn", sl2022.MapFn[once.FN], "timestamp", once.Timestamp)
	// 从缓存中查询
	runtime, err := sm.getRuntime(stationSN)
	if err != nil {
		xlog.Warn("do not handle onReport", "stationSN", stationSN)
		return err
	}
	defer sm.stationRuntime.Store(stationSN, runtime)

	// 任意消息，都更新一下LastMessage 信息（确认是更近的消息）
	if once.Timestamp > runtime.GetLastMessage(once.FN) {
		runtime.SetLastMessage(once.FN, once.Timestamp) // 记录不同 FN 的最新消息时间
	}

	// 只有当消息时间比当前的 LastMessageAt 更新时，才更新最后消息的时间和类型
	if once.Timestamp > runtime.LastMessageAt {
		runtime.LastMessageAt = once.Timestamp          // 消息时间
		runtime.LastMessageType = sl2022.MapFn[once.FN] // 消息类型
	}

	// 对于数据项的解析，支持如下 FN，不同 FN 分别处理
	switch once.FN {
	case sl2022.FN2F, sl2022.FN33: //心跳消息、加报
		// 当设备之前是失联，收到心跳就改为缺报
		if runtime.Flag == dto.StationFlagLost || runtime.Flag == dto.StationFlagUnknown {
			runtime.Flag = dto.StationFlagQB
			xlog.Debug("station flag QB -------------------------------- 1", "sn", stationSN)

			// xlog.Debug("============== sl2022 set station flag: other fn", "ssn", stationSN, "flag", dto.StationFlagName(runtime.Flag))
			sm.dao.SetStationFlag(stationSN, runtime.Flag)
		}
		return nil

	case sl2022.FN34: //小时报
	case sl2022.FNE3, sl2022.FNE4, sl651.FN36: //图像报
	default:
		once.Debug("sl2022 fn do not check flag", "fn", sl2022.MapFn[once.FN])
		return nil
	}

	xlog.Debug("station session on report .... 2", "ssn", stationSN, "fn", sl2022.MapFn[once.FN], "timestamp", once.Timestamp)

	// 按照湖南数据汇聚平台的 14 元素定义，来判断是否缺项（缺少某个元素）
	// var TemplateElementSl2022 = map[string]eleType{
	// 	"1":  {"降水量", "rain"},
	// 	"2":  {"图像", "image"},
	// 	"3":  {"工况", "gongkuang"},
	// 	"4":  {"渗流压力", "osmotic"},
	// 	"5":  {"渗流量", "seepage"},
	// 	"6":  {"表面水平位移", "displace_h"},
	// 	"7":  {"表面垂直位移", "displace_v"},
	// 	"8":  {"水位整编", "hour_water"}, // 小时报水位整编
	// 	"9":  {"降水整编", "hour_rain"},  // 小时报雨量整编
	// 	"10": {"白蚁监测", "termite"},    // 白蚁
	// 	"11": {"气象五要素", "weather"},   // 风速、风向、温度、湿度、大气压
	// 	"12": {"坡面径流", "slope_runoff"},
	// 	"13": {"降水分配", "rain_distri"},
	// 	"14": {"土壤墒情", "soil_moisture"},
	// }

	// 缺项判断，通过 elements 和  TemplateElementSl2022 对比。
	// 缺项的元素的 ele。每次检查图像报和小时报的时候，都重新创建

	// 图像报
	if once.FN == sl2022.FNE3 || once.FN == sl2022.FNE4 || once.FN == sl651.FN36 {
		eleImg := "2"
		if result, ok := runtime.GetLastReportFmt(eleImg); ok {
			if once.image == nil {
				result.Status = dto.EleStatusNOTOK
			} else {
				result.Status = dto.EleStatusOK
				result.Value = once.image
				result.Timestamp = once.Timestamp
			}

			// 更新到 LastReportFmt
			runtime.SetLastReportFmt(eleImg, result)
		} else {
			xlog.Warn("not has eleImg, but get image report", "station_sn", once.StationSN)
		}
	} else if once.FN == sl2022.FN34 {
		// 小时报
		// 解析定义的采集要素，更新到 LastReportFmt
		runtime.RangeLastReportFmt(func(ele string, result *dto.EleDetail) bool {
			switch ele {
			case "1":
				// 小时报，这里检查是小时段雨量
				result.Value = once.hourRain

			case "3":
				result.Value = once.tsGK
			case "4":
				result.Value = once.tsOsmotic
			case "5":
				result.Value = once.tsSeepage

			case "6":
				result.Value = once.tsDisplaceH

			case "7":
				result.Value = once.tsDisplaceV

			case "8":
				if len(once.hourlyWater) != 0 {
					result.Value = once.hourlyWater
				}

			case "9":
				if len(once.hourlyRain) != 0 {
					result.Value = once.hourlyRain
				}

			case "10":
				result.Value = once.termite

			case "11":
				result.Value = once.weather

			case "12":
				result.Value = once.slopeRunoff

			case "13":
				result.Value = once.rainDistri
			case "14":
				result.Value = once.soilMoisture
			default:
				return true
			}

			// nil 说明没数据
			if result.Value == nil {
				result.Status = dto.EleStatusNOTOK
			} else {
				result.Status = dto.EleStatusOK

				// 记录每一个元素的最新结果
				result.Timestamp = once.Timestamp
				runtime.SetLastReportFmt(ele, result)
			}
			return true
		})
	}

	// ==================================================================
	// 小时报、图像报检查完，看看是否有缺项，没有即正常

	// 看还是否有缺项（虽然是实时消息，但是有时候消息时间是错误的）
	tsNow := time.Now().Unix()

	if runtime.LastMessageAt == 0 {
		runtime.Flag = dto.StationFlagUnknown
	} else {
		ts34 := runtime.GetLastMessage(sl2022.FN34)
		// 失联：已经 12个小时没有新消息了
		if tsNow-runtime.LastMessageAt > (LostTimeoutSeconds - 60) {
			runtime.Flag = dto.StationFlagLost
		} else if tsNow-ts34 > (HourlyTimeoutSeconds - 60) {
			// 缺报：缓存中没有找到小时报，或者超过 3 个小时没有小时报
			xlog.Debug("station flag QB -------------------------------- 3", "sn", runtime.StationSN, "ts34", ts34)
			runtime.Flag = dto.StationFlagQB
		} else {
			runtime.Flag = dto.StationFlagOk
		}
	}

	//如果是 OK，再检查缺项
	if runtime.Flag == dto.StationFlagOk {
		runtime.RangeLastReportFmt(func(k string, one *dto.EleDetail) bool {
			if one.Value == nil {
				runtime.Flag = dto.StationFlagQX
				xlog.Debug("station flag QX -------------------------------- 4", "sn", runtime.StationSN, "ele", k)
				return false
			}
			// 如果某一个监测项，3 个小时没更新数据，则设置为缺项
			if once.Timestamp-one.Timestamp > HourlyTimeoutSeconds {
				runtime.Flag = dto.StationFlagQX
				xlog.Debug("station flag QX -------------------------------- 5", "sn", runtime.StationSN, "ele", k)
				return false
			}
			return true
		})
	}

	xlog.Debug("============== sl2022 set station flag: on report", "ssn", stationSN, "flag", dto.StationFlagName(runtime.Flag))
	sm.dao.SetStationFlag(stationSN, runtime.Flag)

	return nil
}
