package dto

import (
	"strings"
	"time"
)

// 设备自动上报的数据包（握手包E2）。如果设备自带gps，则自动更新坐标。
type ReqStationAuto struct {
	StationSN string `form:"station_sn"      json:"station_sn"   binding:"required"` //测站地址
	Category  string `form:"category"        json:"category"`                        //测站类型
	Lng       string `form:"lng" json:"lng,omitempty"`                               //经度
	Lat       string `form:"lat" json:"lat,omitempty"`                               //维度

	//仅 NC1800用上
	// CompanyID int64 `json:"company_id,omitempty"        form:"company_id"` // 公司 ID：仅 NC1800 在握手的时候，默认填充此字段
}

// 迁移数据：初始化测站的编码和 name、address
type ReqStationMigration struct {
	StationSN string `form:"station_sn"     json:"station_sn"` //测站地址
	Name      string `form:"name"           json:"name"`
	Desc      string `form:"desc"           json:"desc"`
}

// from web：手动建站、修改测站信息
type ReqStationCreate struct {
	StationSN string `form:"station_sn"     json:"station_sn,omitempty"` //测站地址
	Password  string `form:"password"       json:"password,omitempty"`
	Name      string `form:"name"           json:"name,omitempty"`
	Desc      string `form:"desc"           json:"desc,omitempty"`
	Category  string `form:"category"       json:"category,omitempty"` //category 参数Category

	//区域：线上建站可以选择区域
	ProvinceID int64 `form:"province_id"    json:"province_id"` //省ID
	CityID     int64 `form:"city_id"        json:"city_id"`     //市ID
	DistrictID int64 `form:"district_id"    json:"district_id"` //区县ID

	//坐标：手机 APP 建站要用坐标
	Lng string `form:"lng" json:"lng"` //经度
	Lat string `form:"lat" json:"lat"` //维度

	IMEI     string `form:"imei" json:"imei,omitempty"`           //设备硬件ID
	DeviceSN string `form:"device_sn" json:"device_sn,omitempty"` //机身编码
	Protocal string `form:"protocal" json:"protocal,omitempty"`   //设备配置的协议：sl651 or sl427

	DataPoints []string `form:"data_points" json:"data_points"` //仅手机 APP 建站的时候用到，暂时仅支持 SL651

	CompanyID int64 `form:"company_id"     json:"company_id"` //所属公司ID， 后端填充
}

// 主要是在后台管理端更新测站相关信息
type ReqStationUpdate struct {
	ID        int64  `form:"id"             json:"id"            binding:"required"` //更新测站
	StationSN string `form:"station_sn"     json:"station_sn"    binding:"required"` // 不做为参数，同步 projectCode 用
	Category  string `form:"category"       json:"category,omitempty"`               //category 参数Category

	Name string `form:"name"           json:"name,omitempty"`
	Desc string `form:"desc"           json:"desc,omitempty"`

	//区域
	ProvinceID int64 `form:"province_id"    json:"province_id,omitempty"` //省ID
	CityID     int64 `form:"city_id"        json:"city_id,omitempty"`     //市ID
	DistrictID int64 `form:"district_id"    json:"district_id"`           //区县ID,可能是 0，所以这里不能 omit

	//坐标
	Lng string `form:"lng" json:"lng,omitempty"` //经度
	Lat string `form:"lat" json:"lat,omitempty"` //维度

	Protocal    string `form:"protocal"  json:"protocal,omitempty"`           //协议：sl651 or sl427
	ProjectCode string `form:"project_code"    json:"project_code,omitempty"` // 项目id

	// CompanyID int64 `form:"-"     json:"-"` //所属公司ID，后台填充，不可以修改，只能通过修改设备公司来同步
	// OwnerName   string  `form:"owner_name"  json:"owner_name,omitempty"`     //运维联系人
	// OwnerPhone  string  `form:"owner_phone"  json:"owner_phone,omitempty"`   //运维联系电话
	// WaterOffset float64 `form:"water_offset"  json:"water_offset,omitempty"` //水位校正基值，浮点数，单位米

	Meta map[string]any `form:"meta"  json:"meta,omitempty"`
}

// 区域
type ReqArea struct {
	ProvinceID int64 `form:"province_id"    json:"province_id,omitempty"` //省ID
	CityID     int64 `form:"city_id"        json:"city_id,omitempty"`     //市ID
	DistrictID int64 `form:"district_id"    json:"district_id,omitempty"` //区县ID
}

type ReqAreaPointType struct {
	ProvinceID  int64  `json:"province_id"   form:"province_id,omitempty"` //省ID
	CityID      int64  `json:"city_id"       form:"city_id,omitempty"`     //市ID
	DistrictID  int64  `json:"district_id"   form:"district_id,omitempty"` //区ID
	ProjectCode string `json:"project_code"    form:"project_code"`        // 项目Code
}

type ReqStationQuery struct {
	StationSN      string      `form:"station_sn"   json:"station_sn,omitempty"`          //测站编码
	Name           string      `form:"name"         json:"name,omitempty"`                //测站名字，支持模糊搜索
	Category       string      `form:"category"     json:"category,omitempty"`            //参考651的定义。可根据类型查询水位站，为空则返回全部
	Protocal       string      `form:"protocal"     json:"protocal,omitempty"`            // 按协议查询
	Status         string      `form:"status"       json:"status,omitempty"`              //在线状态
	Flag           StationFlag `form:"flag"         json:"flag,omitempty"`                //station flag
	ContractorCode string      `form:"contractor_code"  json:"contractor_code,omitempty"` // 按承建商查询
	SensorEnum     string      `form:"sensor_enum"  json:"sensor_enum,omitempty"`         // 测站的测点类型

	//区域 ID
	ProvinceID int64 `form:"province_id"    json:"province_id,omitempty"` //省ID
	CityID     int64 `form:"city_id"        json:"city_id,omitempty"`     //市ID
	DistrictID int64 `form:"district_id"    json:"district_id,omitempty"` //区县ID

	//区域 code

	ProjectCode string `form:"project_code"     json:"project_code,omitempty"` // 项目code
	IMEI        string `form:"imei"           json:"imei,omitempty"`           //设备硬件ID
}

// 废弃 stationSensorMap 这个表 SensorEnum 使用 PointType 来代替
type ReqExportAndEnq struct {
	StationSN  string `form:"station_sn"  json:"station_sn"  binding:"required"` //测站编码
	SensorEnum string `form:"sensor_enum" json:"sensor_enum" binding:"required"` //测点类型，此处仅支持 rain 和 water 两种
}

// 整编时序数据处理之后的格式
// 070116.00,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0
type RespExportData struct {
	StationSN  string   `json:"station_sn"`  //测站编码
	SensorEnum string   `json:"sensor_enum"` //传感器枚举值，参考字典 dict.elem.sl651
	Start      int64    `json:"start"`       //开始时间,unix 时间戳
	End        int64    `json:"end"`         //截止时间,unix 时间戳
	Path       string   `json:"path"`        //整编文件路径
	Data       []string `json:"data"`        //整编数据,key-value，key:mmddhh.00 , value:采样数据序列
}

// =====================================
// 测站总览
// 按照 status 统计
type RespStationStatusAnalytics struct {
	Total      int64          `json:"station_total"`   //测站总数
	StationMap map[string]int `json:"station_map"`     //测站区域分布
	Online     int64          `json:"station_online"`  //在线数
	Offline    int64          `json:"station_offline"` //离线数
}

// 按照区域条件，某个区域的统计结果
type StationFlagInfo struct {
	Name    string `json:"name"`    // 区域或者承建商名字
	Code    string `json:"code"`    // 区域或者承建商编码
	Total   int64  `json:"total"`   // 总数
	Lost    int64  `json:"lost"`    // 失联
	Normal  int64  `json:"normal"`  // 正常
	Missing int64  `json:"missing"` // 缺项
	Lacking int64  `json:"lacking"` // 缺报
	Unknown int64  `json:"unknown"` // 未知
}

type RespStationFlagAnalytics struct {
	Total   int64                       `json:"total"`   // 总数
	Unknown int64                       `json:"unknown"` // 未知
	Lost    int64                       `json:"lost"`    // 失联：超过 12 小时没有任何消息上报
	Normal  int64                       `json:"normal"`  // 正常
	Missing int64                       `json:"missing"` // 缺项：有小时报，但是缺少采集要素
	Lacking int64                       `json:"lacking"` // 缺报：超过 3 小时没有上报小时报
	Result  map[string]*StationFlagInfo `json:"result"`  // key :area_name
}

// =====================================
type ReqPacketLog struct {
	StationSN string `form:"station_sn"           json:"station_sn,omitempty"`
	Fn        string `form:"fn"                   json:"fn,omitempty"`
	Direct    string `form:"direct"               json:"direct,omitempty"`
	Protocal  string `form:"protocal"             json:"protocal,omitempty"`
	Start     int64  `form:"start"                json:"start,omitempty"` //开始时间,unix 时间戳
	End       int64  `form:"end"                  json:"end,omitempty"`   //截止时间,unix 时间戳
}

// 汇总所有测站，不同的功能码的数据报统计
type RespPacketCount struct {
	Fn    string `json:"fn"`    //功能码
	Count int64  `json:"count"` //计数
}
type RespPacketCheck struct {
	StationSN string             `json:"station_sn,omitempty"` //测站编码
	Lost      []string           `json:"lost,omitempty"`       //存储每一次的缺失的小时报时间
	Count     []*RespPacketCount `json:"count,omitempty"`      //各报文数量累计
	Repeat    int                `json:"repeat,omitempty"`
}

type ReqListEarlyWarning struct {
	PageInfo
	ProjectCode string          `form:"project_code"  json:"project_code"      binding:"required"`
	StationSN   string          `form:"station_sn"  json:"station_sn"`
	PointType   string          `form:"point_type"   json:"point_type"`
	PointName   string          `form:"point_name"  json:"point_name"`
	Status      EarlyWarnStatus `form:"status"      json:"status"`
	Level       EarlyWarnLevel  `form:"level"        json:"level"`
	StartTime   string          `form:"start_time"   json:"start_time"`
	EndTime     string          `form:"end_time"     json:"end_time"`
}

type ReqEarlyWarning struct {
	ID     int64  `form:"id"           json:"id"` //更新测站
	Status int32  `form:"status"       json:"status"`
	Result string `form:"result"       json:"result"`
}

// 预警总览
type RespEarlyWarnDashboard struct {
	Total int64 `json:"total"` // 预警总数
	// Water      int64            `json:"water"`        // 水位预警数量
	// Rain       int64            `json:"rain"`         // 雨量预警数量
	WarnTypeMap map[string]int64 `json:"warn_type_map"` // 根据预警类型数量统计
	System      int64            `json:"system"`        // 系统预警数量
	LevelMap    map[int32]int64  `json:"level_map"`     // 等级预警
	DayWarnMap  map[string]int64 `json:"day_warn_map"`  // 天预警数量
}

// ----------------------------------- 测站的实时数据

type StationFlag int

const (
	StationFlagOk      = 1 // 正常
	StationFlagQX      = 2 // 缺项：某一个监测项最近三个小时没有更新数据
	StationFlagQB      = 3 // 缺报：小时报最近三个小时没有新数据
	StationFlagLost    = 4 // 失联：小时报超过 12 个小时没有更新数据
	StationFlagUnknown = 5 // 未知，默认值，从未上报过数据
)

func StationFlagName(flag StationFlag) string {
	switch flag {
	case StationFlagOk:
		return "正常"
	case StationFlagQX:
		return "缺项"
	case StationFlagQB:
		return "缺报"
	case StationFlagLost:
		return "失联"
	case StationFlagUnknown:
		return "未知"
	default:
		return "无效"
	}
}

func IsValidFlag(flag StationFlag) bool {
	return flag == StationFlagOk || flag == StationFlagQX || flag == StationFlagQB || flag == StationFlagLost
}

type EleStatus int

const (
	// EleStatusNotDefine EleStatus = 0 // 未定义此元素
	EleStatusNOTOK EleStatus = 1 // 定义了此元素，但是漏掉或者超时
	EleStatusOK              = 2 // 定义了此元素，且没有漏掉
)

// 对于每一个元素，转换成结构化的数据，web 端用
type EleDetail struct {
	EleNum    string            `json:"ele_num"`   // 1 ～ 14 的编号
	Name      string            `json:"name"`      // 名字
	Header    map[string]string `json:"header"`    // 字段描述
	Status    EleStatus         `json:"status"`    // 参考EleStatus ， 0：未定义此要素， 1：定义了但是漏掉  2：定义了且没有漏掉
	Timestamp int64             `json:"timestamp"` // 时序数据时间戳
	Value     any               `json:"value"`     // 时序数据值
}

type StationRuntime struct {
	UpdatedAt time.Time   `json:"-"` //限制 1 个小时更新一次，避免每次都要更新 runtime
	StationSN string      `json:"station_sn"`
	Elements  string      `json:"elements"` //要素编号
	Flag      StationFlag `json:"flag"`     // 当前状态
	// lastReport 缓存最新上报的消息（解析之后填充）
	// LastReportFmt   map[string]*EleDetail `json:"last_report_fmt"`   // key 是编号，val 是时序数据格式化之后的结果，用于 web 端使用
	// LastMessage     map[uint8]int64       `json:"last_message"`      // key 是 FN，用于判断是否缺报
	LastMessageAt   int64  `json:"last_message_at"`   // 最近一条消息的时间，包括心跳、加报、小时报等
	LastMessageType string `json:"last_message_type"` // 最近一条消息的类型，包括心跳、加报、小时报等
}

// 汇报当前状态详情，仅缺报、缺项
// TODO：待完善
func (rt *StationRuntime) FlagReport() string {
	sb := strings.Builder{}
	if rt.Flag == StationFlagQX {
		sb.WriteString("缺项：")
	} else if rt.Flag == StationFlagQB {
		sb.WriteString("缺报：")
	}
	return sb.String()
}

// 测站统计：对应 device model 的统计
type DeviceModelCount struct {
	Model string `json:"model"` // 设备型号
	Count int64  `json:"count"` // 数量
}

type RespDeviceModelAnalytics struct {
	Total   int64              `json:"total"`   // 总数
	Details []DeviceModelCount `json:"details"` // 详细统计
}
