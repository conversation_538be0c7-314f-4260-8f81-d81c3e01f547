package model

import (
	"time"

	"bs.com/ebox/global"
	"bs.com/ebox/internal/gw/dto"
)

// 测站故障记录
// TODO 故障也要和公司隔离
type FaultInfo struct {
	BaseModelCreateUpdate

	CompanyID int64  `gorm:"index;comment:公司ID" json:"company_id"`
	IMEI      string `gorm:"type:varchar(64);index;comment:设备IMEI" json:"imei"`
	SSN       string `gorm:"type:varchar(64);index;comment:测站编码" json:"ssn"`
	Type      string `gorm:"index;comment:错误类型码" json:"type"`
	Status    int    `gorm:"index;default:1;comment:消息状态" json:"status"` // 状态, 1 : open 未处理，2:close:已处理， 0：无效。默认未处理
	Count     int    `gorm:"default:1;comment:错误计数" json:"count"`        // 错误计数，默认 1
	Desc      string `gorm:"type:varchar(255);comment:故障描述" json:"desc"`
	Remark    string `gorm:"type:varchar(255);comment:故障处理备注" json:"remark"`
	ClosedBy  int64  `gorm:"comment:操作者 ID"  json:"closed_by"`
}

// ======================================================================================
func (d *daoIOT) AddFaultInfo(imei, ssn, faultType string, desc string) error {
	if imei == "" && ssn == "" {
		// xlog.Warn("add fault info: invalid imei and ssn")
		return nil
	}
	if len(desc) > 250 {
		desc = desc[:250]
	}
	var compID int64

	// 查重：避免同样的问题，同一天内重复上报
	data := make(map[string]interface{})

	if imei == "" {
		imei = "unknown"
		data["ssn"] = ssn //测站ID

		err := d.db.Model(&Station{}).Select("company_id").Where("station_sn =?", ssn).First(&compID).Error
		if err != nil {
			// xlog.Error("add fault info: station not create", "ssn", ssn)
			compID = global.COMPANY_ID_BEITHING
		}
	}

	// 默认是用 ssn（原则上 ssn 和 imei 只应该有一个）
	if ssn == "" {
		ssn = "unknown"
		data["imei"] = imei //设备ID

		err := d.db.Model(&Device{}).Select("company_id").Where("imei =?", imei).First(&compID).Error
		if err != nil {
			// xlog.Error("add fault info: invalid imei", "imei", imei)
			compID = global.COMPANY_ID_BEITHING
		}
	}

	addNew := false
	var old FaultInfo
	if faultType == "invalid_data" {
		// 无效数据故障 invalid_data，总是新增
		addNew = true
	} else {
		data["type"] = faultType //故障类型
		data["status"] = 1       //未处理

		//查找最新的、未处理的、同类型的、报错
		err := d.db.Model(&FaultInfo{}).Where(data).Order("id DESC").First(&old).Error

		if err != nil {
			// 如果没有找到，则新增
			addNew = true
		} else if old.CreatedAt.Add(24*time.Hour).Unix() < time.Now().Unix() {
			// 找到但是已经超时了 1 天，也新增
			addNew = true
		}
	}

	// 添加新的故障消息
	if addNew {
		f := &FaultInfo{
			IMEI:      imei,
			SSN:       ssn,
			Type:      faultType,
			Desc:      desc,
			CompanyID: compID,
		}
		return d.db.Model(&FaultInfo{}).Create(f).Error
	}

	// 不新增，只是增加计数
	m := map[string]any{
		"count":      old.Count + 1, // 更新计数
		"updated_at": time.Now(),    // 更新时间
	}

	return d.db.Model(&FaultInfo{}).Where("id = ?", old.ID).Updates(m).Error
}

// 查询故障，可以有测站编码作为参数
func (d *daoIOT) ListFault(company_id int64, pi *dto.PageInfo, param *dto.ReqFaultQuery) ([]*FaultInfo, error) {
	var ret []*FaultInfo

	session := d.db.Model(&FaultInfo{})
	if company_id != global.COMPANY_ID_BEITHING {
		session = session.Where("company_id = ?", company_id)
	}
	data := StructToMap(param)

	if param.IMEI != "" {
		session = session.Where("imei like ?", "%"+param.IMEI)
		delete(data, "imei")
	}

	// 测站管理只查询测站故障
	if param.ForStation {
		session = session.Where("ssn != 'unknown'")
		delete(data, "for_station")
	}

	session = session.Where(data).Order("id DESC")

	err := PagedFind(session, &ret, pi)

	return ret, err
}

// 关闭故障问题：支持批量处理
func (d *daoIOT) CloseFault(fid int64, remark string, uid int64) error {
	data := make(map[string]interface{})
	data["status"] = 2
	data["closed_by"] = uid
	data["remark"] = remark

	return d.db.Model(&FaultInfo{}).Where("id =  ?", fid).Updates(data).Error
}
