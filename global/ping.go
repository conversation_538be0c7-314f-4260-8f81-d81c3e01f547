package global

import (
	"context"
	"fmt"
	"os"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/pkg/httpclient"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xwg"
)

type Ping struct {
	Host string
	Port uint16
}

func NewPing() xwg.IService {
	host := "*************"

	cfg := config.Get()
	if cfg.DeployID == "hunan2" {
		// 私网 IP
		host = "**************"
	}

	port := uint16(60010)

	return &Ping{
		Host: host,
		Port: port,
	}
}

func (p *Ping) Run(ctx context.Context) error {

	url := fmt.Sprintf("http://%s:%d/ping", p.Host, p.Port)
	tc := time.NewTicker(time.Second * 60)
	cfg := config.Get()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-tc.C:
			// 心跳消息
			xlog.Debug("-------------------- ping")
			resp, err := httpclient.POST(url, map[string]interface{}{
				"name":           cfg.ServiceGW.Name,
				"deploy_id":      cfg.DeployID,
				"deploy_license": cfg.DeployLicense,
				"version":        Version,
			})
			if err != nil {
				xlog.Debug("ping error:" + err.Error())
				return err
			}
			// 非法使用
			xlog.Debug("response", "resp", resp)
			if resp.Code == 10086 {
				os.Exit(1)
			}
		}
	}
}

func (p *Ping) Name() string {
	return "ping"
}
