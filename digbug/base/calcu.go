package base

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"strconv"
)

func calcu(filename string) (sum float64) {
	fmt.Println("filename : ", filename)

	fd, err := os.Open(filename)

	if err != nil {
		fmt.Println("read error:", err)
		return
	}
	defer fd.Close()

	buff := bufio.NewReader(fd)
	for {
		line, _, eof := buff.ReadLine()
		if eof == io.EOF {
			break
		}

		tmp, err := strconv.ParseFloat(string(line), 64)
		if err != nil {
			fmt.Println(string(line))
			fmt.Println("parse float err : ", err.Error())
			return
		}
		sum += tmp
	}

	fmt.Println("success , sum = ", sum)

	return
}
