package main

import (
	"log"

	"github.com/spf13/cobra"

	_ "bs.com/ebox/digbug/base"
	"bs.com/ebox/digbug/cmd"
	_ "bs.com/ebox/digbug/hunan"
	_ "bs.com/ebox/digbug/mq"

	//	_ "bs.com/ebox/digbug/pg"
	_ "bs.com/ebox/digbug/shuili"
	_ "bs.com/ebox/digbug/video"
)

// type logWriter struct {
// }

// func (w logWriter) Write(bytes []byte) (int, error) {
// 	return fmt.Print(time.Now().Local().Format("2006-01-02 15:04:05") + " " + string(bytes))
// }

func main() {
	log.SetFlags(0)

	// Root cmd
	var rootCmd = &cobra.Command{
		Use: "command",
		PersistentPreRun: func(cmdCobra *cobra.Command, args []string) {
			// Set HTTP server address
			cmd.PreInit()
		},

		PersistentPostRun: func(cmdCobra *cobra.Command, args []string) {
		},
	}
	cmd.AddCommands(rootCmd)

	if err := rootCmd.Execute(); err != nil {
		log.Fatal(err)
	}
}
