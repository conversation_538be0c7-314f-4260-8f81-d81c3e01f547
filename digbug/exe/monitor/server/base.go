package server

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

//按照分层设计的原则，接口的处理分为三层：controller、service、dao。
//前期为了简化，仅实现 controller 和 dao层，后期如果需要封装RPC接口，再拆解出service。

type BaseController struct {
}

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Payload interface{} `json:"payload,omitempty"`
}

func (b *BaseController) GetRemoteIP(ctx *gin.Context) string {
	ip := ctx.Request.Header.Get("X-Real-IP")
	if ip == "" {
		ip = ctx.Request.Header.Get("X-Forwarded-For")
	}
	return ip
}

// 普通返回
func (b *BaseController) ResponseOK(ctx *gin.Context) {
	resp := &Response{
		Code:    0,
		Message: "success",
	}

	ctx.JSON(http.StatusOK, resp)
}

// 普通返回带数据
func (b *BaseController) ResponseData(ctx *gin.Context, data interface{}) {
	resp := &Response{
		Code:    0,
		Message: "success",
		Payload: data,
	}

	ctx.JSON(http.StatusOK, resp)
}

// 错误返回
func (b *BaseController) ResponseError(ctx *gin.Context, code int, err error) {
	resp := &Response{
		Code:    code,
		Payload: nil,
	}

	if err != nil {
		resp.Message = err.Error()
	} else {
		resp.Message = "unknown error"
	}

	ctx.JSON(http.StatusOK, resp)
}
