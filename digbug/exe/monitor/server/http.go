package server

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"bs.com/ebox/pkg/httpserver"
	"bs.com/ebox/pkg/xjwt"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xwg"
	"github.com/gin-gonic/gin"
)

type HttpServer struct {
}

func NewHttpServerMonitor(port uint16) xwg.IService {
	xlog.Info("new http server", "port", port)
	//初始化路由
	handler := InitHttpHandler()

	//转换为string
	portStr := strconv.FormatInt(int64(port), 10)
	return httpserver.NewServer(
		"monitor-http-server",
		handler,
		httpserver.Port(portStr), httpserver.ReadTimeout(5*time.Second), // options
	)
}

func InitHttpHandler() http.Handler {
	gin.SetMode(gin.ReleaseMode)
	// gin.SetMode(gin.DebugMode)
	engine := gin.New()
	engine.Use(gin.Recovery())

	// engine.Use(Logger())

	//添加路由
	engine.NoRoute(func(c *gin.Context) {
		c.J<PERSON>(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "not found",
		})
	})

	NewToolGroup().Router(engine)

	return engine
}

type ToolGroup struct {
	BaseController
}

func NewToolGroup() *ToolGroup {
	return &ToolGroup{
		BaseController: BaseController{},
	}
}

func (t *ToolGroup) Router(r *gin.Engine) {
	r.POST("/ping", t.pingHandler)
	r.POST("/gen_license", t.genLicenseHandler)
}

func (t *ToolGroup) pingHandler(ctx *gin.Context) {
	req := struct {
		Name      string `json:"name"            form:"name"       binding:"required"`
		DeployID  string `json:"deploy_id"       form:"deploy_id"  binding:"required"`
		DeployLic string `json:"deploy_lic"      form:"deploy_lic"`
		Version   string `json:"version"         form:"version"`
	}{}

	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		t.ResponseError(ctx, 1000, err)
		return
	}
	ip := ctx.ClientIP()
	xlog.Debug("ping", "ip", ip, "name", req.Name, "deploy_id", req.DeployID, "version", req.Version)

	t.ResponseData(ctx, gin.H{"pong": req.Name})
	// t.ResponseError(ctx, 10086, errors.New("test error"))
}

func (t *ToolGroup) genLicenseHandler(ctx *gin.Context) {
	req := struct {
		DeployID string `json:"deploy_id" form:"deploy_id"     binding:"required"` //必须是先在后台管理端添加
		Message  string `json:"message"   form:"message"       binding:"required"` //部署机器信息
	}{}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		t.ResponseError(ctx, 1000, err)
		return
	}
	license := xjwt.GenLicense(req.DeployID, req.Message)

	fmt.Println("deploy_id: ", req.DeployID)
	fmt.Println("deploy_license: ", license)
	// 返回成功
	t.ResponseData(ctx, gin.H{
		"desc": "请向管理员提供 deploy_id，以获取license",
	})
}
