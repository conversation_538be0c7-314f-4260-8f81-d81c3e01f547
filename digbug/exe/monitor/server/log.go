package server

import (
	"bytes"
	"io"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"bs.com/ebox/internal/gw/model"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xutils"
)

// ILogger 记录每次请求的请求信息和响应信息

type bodyLogWriter struct {
	gin.ResponseWriter
	resp *bytes.Buffer // response copy
}

// copy response to bodyLogger
func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.resp.Write(b)
	return w.ResponseWriter.Write(b)
}

var unlogedregexPattern *regexp.Regexp
var unlogedPathList = []string{
	//以下列路径为前缀的接口，不需要校验token
	"/static",
	"/assets",
	"/cdn",
	"/page",
	"/favicon.ico",
	"/page/debug/pprof/",
	"/api/iot/add_firmware",
	"/api/v1/upload",
	"/api/v1/download",

	"/api/system/area_info",
	"/api/data/mx/gnss_data",
}

func init() {
	unlogedregexPattern, _ = regexp.Compile(strings.Join(unlogedPathList, "|"))
}

// const maxBodyLen = 10 * 1024 //10KB

func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 请求开始时间
		start := time.Now()
		method := c.Request.Method
		path := c.Request.URL.Path

		//某些请求不记录日志，比如静态资源，比如上传、下载文件
		// 对于OPTIONS请求，也不记录日志
		if unlogedregexPattern.MatchString(path) || method == "OPTIONS" {
			c.Next()
			return
		}

		var reqBuf []byte
		var respBuf []byte
		_ = respBuf

		// 捕获响应体
		bw := &bodyLogWriter{
			resp:           new(bytes.Buffer),
			ResponseWriter: c.Writer,
		}
		c.Writer = bw

		// Check if the request is multipart/form-data
		is_upload := false
		contentType := c.Request.Header.Get("Content-Type")
		if !strings.Contains(contentType, "multipart/form-data") {
			is_upload = true
			if c.Request.Body != nil {
				reqBuf, _ = io.ReadAll(c.Request.Body)
				c.Request.Body = io.NopCloser(bytes.NewBuffer(reqBuf))
			}
		} else {
			xlog.Debug("Multipart form detected, skipping body logging.")
		}

		// Process request
		c.Next()

		// 捕获响应体
		if strings.HasPrefix(path, "/api/") && !is_upload {
			respBuf = bw.resp.Bytes()
		}

		// 请求结束时间和其他信息
		latency := time.Since(start)
		ip := c.ClientIP()
		phone := c.GetString("phone")
		reqUrl := c.Request.URL.String()
		reqParam := xutils.CleanString(string(reqBuf))

		if phone == "" || phone == "18665991286" || phone == "17665352980" || reqUrl == "/api/system/access_log" {
			// do nothing
		} else {
			_ = model.NewTemplateRepo[model.AccessLog]().CreateT(
				&model.AccessLog{
					Phone: phone,
					Url:   reqUrl,
					IP:    ip + ":" + model.ParseReginByIP(ip),
				})
		}

		// 记录日志
		xlog.Debug("http request",
			"latency", latency.String(),
			"ip", ip,
			"method", method,
			"path", reqUrl,
			"request", reqParam,
			"resp", xutils.CleanString(string(respBuf)),
		)
	}
}
