package server

import (
	"context"

	"bs.com/ebox/pkg/xmqtt"
	"bs.com/ebox/pkg/xwg"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

type MqttServer struct {
	host string
	// mqtt 三元组
	ClientID string `json:"client_id"`
	Username string `json:"username"`
	Password string `json:"password"`

	ctx    context.Context
	cancel context.CancelFunc

	client mqtt.Client // mqtt 客户端
}

func NewMqttServer(ctx context.Context, host string, clientID string, username string, password string) xwg.IService {
	s := &MqttServer{
		host:     host,
		ClientID: clientID,
		Username: username,
		Password: password,
	}

	s.client = xmqtt.NewClientSimple(host, clientID, username, password, 60)
	s.ctx, s.cancel = context.WithCancel(ctx)

	return s
}

func (s *MqttServer) Name() string {
	return "mqtt-server"
}

func (s *MqttServer) Run(ctx context.Context) error {
	return nil
}
