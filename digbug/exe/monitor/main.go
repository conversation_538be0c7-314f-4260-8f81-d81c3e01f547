package main

import (
	"fmt"
	"time"

	"bs.com/ebox/digbug/exe/monitor/server"
	"bs.com/ebox/pkg/xwg"
)

func main() {
	fmt.Println("Hello, World!")

	wg := xwg.NewWorkerGroup()

	// http
	wg.Go(server.NewHttpServerMonitor(60010))

	// // mqtt
	// mqttHost := ""
	// mqttClientID := ""
	// mqttUsername := ""
	// mqttPassword := ""
	// wg.Go(server.NewMqttServer(context.Background(), mqttHost, mqttClientID, mqttUsername, mqttPassword))

	wg.Wait()

	time.Sleep(200 * time.Millisecond) //200ms
	fmt.Println("by")
}
