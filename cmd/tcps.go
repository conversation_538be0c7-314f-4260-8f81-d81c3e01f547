package cmd

import (
	"fmt"
	"time"

	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xwg"
	"github.com/spf13/cobra"

	"bs.com/ebox/internal/tcps"
)

var TCPS = &cobra.Command{
	Use:   "tcps",
	Short: "start tcp server demo",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		//初始化logger
		level, name, path, format := "debug", "", "", "text"
		xlog.InitLogger(format, "tcps", level, name, path)
	},
	Run: func(cmd *cobra.Command, args []string) {

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		//运行 shell
		wg.Go(tcps.NewShell())

		//运行 tcp server
		wg.Go(tcps.NewServerTCP())

		wg.Wait()

		time.Sleep(100 * time.Millisecond)
		fmt.Println("by")
	},
}
