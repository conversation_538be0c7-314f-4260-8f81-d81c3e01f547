package cmd

import (
	"fmt"
	"os"
	"time"

	"github.com/spf13/cobra"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/shui651"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xwg"
)

var Shui651 = &cobra.Command{
	Use:   "shui651",
	Short: "start shui server",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		slCfg := config.Get().ServiceSl651
		if slCfg == nil {
			fmt.Println("invalid config")
			os.Exit(1)
		}
		misc := config.Get().Misc

		// 如果容器环境变量设置了 AppName，则使用此 AppName 作为此实例的 name
		app_name := os.Getenv("AppName")
		if app_name != "" {
			slCfg.Name = app_name
		}

		//初始化logger
		level, name, path, format := misc.LogLevel, slCfg.Name, misc.PathLog, misc.Format

		//初始化logger
		xlog.InitLogger(format, name, level, name, path)

		global.CheckLicense() //检查 license 信息
	},
	Run: func(cmd *cobra.Command, args []string) {

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		//运行 shell
		wg.Go(shui651.NewSlShell())

		//运行 tcp server
		wg.Go(shui651.NewServerShui())

		wg.Wait()

		time.Sleep(100 * time.Millisecond)
		fmt.Println("by")

	},
}
