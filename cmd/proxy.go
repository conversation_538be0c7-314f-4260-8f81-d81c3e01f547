package cmd

import (
	"fmt"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/proxy"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xwg"
	"github.com/spf13/cobra"
)

var Proxy = &cobra.Command{
	Use:   "proxy",
	Short: "start dayu proxy server",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get().ServiceProxy
		misc := config.Get().Misc

		//初始化logger
		level, name, path, format := misc.LogLevel, cfg.Name, misc.PathLog, misc.Format
		xlog.InitLogger(format, cmd.Use, level, name, path)

		//init
		global.InitRedis(1)

		global.CheckLicense() //检查 license 信息
	},
	Run: func(cmd *cobra.Command, args []string) {

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		//运行 shell
		wg.Go(proxy.NewProxyShell())

		//运行 tcp server
		wg.Go(proxy.NewProxyServer())

		fmt.Println("proxy server started")

		wg.Wait()

		time.Sleep(100 * time.Millisecond)
		fmt.Println("by")
	},
}
