package cmd

import (
	"fmt"
	"time"

	"bs.com/ebox/pkg/xwg"
	"github.com/spf13/cobra"

	"bs.com/ebox/internal/mtrip"
)

var Mtrip = &cobra.Command{
	Use:   "mtrip",
	Short: "start mtrip",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		//init
	},
	Run: func(cmd *cobra.Command, args []string) {

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		tcpsName := "mtrip"
		var tcpPort uint16 = 62101

		//运行 shell
		wg.Go(mtrip.NewShell(tcpsName))

		//运行 tcp server
		wg.Go(mtrip.NewServer(tcpsName, tcpPort))

		wg.Wait()

		time.Sleep(100 * time.Millisecond)
		fmt.Println("by")
	},
}
