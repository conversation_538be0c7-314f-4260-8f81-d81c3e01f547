package main

import (
	"fmt"
	"os"
	"time"

	"bs.com/ebox/internal/gw/business"
	"bs.com/ebox/internal/gw/model"
	"bs.com/ebox/internal/gw/tasks"
	"github.com/spf13/cobra"

	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xwg"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/gw"
	"bs.com/ebox/internal/gw/iotmqtt"
	"bs.com/ebox/internal/gw/iottcp"
)

// 替换gateway，作为http 服务器
var GW = &cobra.Command{
	Use:   "gw",
	Short: "start gw server",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		xlog.Debugf("gw pre run ...")
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, cfg.ServiceGW.Name, misc.PathLog, misc.Format

		//初始化logger
		// level = "debug"
		xlog.InitLogger(format, cmd.Use, level, name, path)
		// xlog.SetLevel(level)

		global.CheckLicense() //检查 license 信息

		//初始化mysql 和 redis
		global.InitRedis(0)

		//检查influx数据库链接是否 ok
		global.InitInflux()

		// minio存储服务
		global.InitMinio()

		// dbName := cfg.Mysql.DBName
		// global.InitMysql(dbName)
		dbName := cfg.Postgres.DBName
		// 检查数据库是否存在，如果不存在，则autoMigrate之后，创建数据库
		dbExisted := global.CheckPGDBExisted(dbName)

		// 初始化数据库的连接
		global.InitPostgres(dbName)

		// 初始化查询表
		_ = model.NewToolInstance()

		//创建表 automigrate
		err := model.PGAutoMigrate()
		if err != nil {
			xlog.Error("auto migrate err : ", err.Error())
			os.Exit(1)
		} else {
			xlog.Info("auto migrate success.")
		}

		// 如果开始时候,数据库不存在,则导入初始化一些必要的数据
		if !dbExisted {
			model.InitDBData()
		}
		// 调试阶段，总运行初始化 APIResource 表
		model.InitApiResource()
	},

	Run: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		wg.Go(NewShell())

		//运行 shell
		wg.Go(gw.NewGatewayShell())

		wg.Go(tasks.NewAsynqMan())     //异步任务管理器
		wg.Go(tasks.NewInitDeinit())   //pre-init and post-deinit
		wg.Go(tasks.NewCronService())  //定时任务管理器
		wg.Go(tasks.NewEventHandler()) //mqtt事件处理

		wg.Go(business.NewServerBusiness())                   // 内部统一使用nats业务数据处理
		wg.Go(iotmqtt.NewServerIotMqtt())                     //统一的基于mqtt的运维管理入口
		wg.Go(iottcp.NewServiceIotTCP(cfg.ServiceGW.TcpPort)) //tcp server
		wg.Go(gw.NewHttpServer(cfg.ServiceGW.HttpPort))       //运行http server

		wg.Go(global.NewPing())
		wg.Wait()

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}
