package main

import (
	"fmt"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/gw/model"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xwg"
	"github.com/abiosoft/ishell/v2"
	"github.com/spf13/cobra"
)

// ------------------------------------------------------------------- shui651
var (
	dropI = &ishell.Cmd{
		Name: "dropIndex",
		Help: "drop all index",
		Func: func(c *ishell.Context) {
			model.DropIndex()
		},
	}

	dropC = &ishell.Cmd{
		Name: "dropConstaint",
		Help: "drop all constraint",
		Func: func(c *ishell.Context) {
			model.DropConstaint()
		},
	}

	mig = &ishell.Cmd{
		Name: "migrate",
		Help: "auto mig all table",
		Func: func(c *ishell.Context) {
			model.PGAutoMigrate()
		},
	}

	addSuperUser = &ishell.Cmd{
		Name: "addUser",
		Help: "auto mig all table",
		Func: func(c *ishell.Context) {
			model.InitUserTable()
		},
	}
)

func NewShell() xwg.IService {
	arr := []*ishell.Cmd{
		mig, dropC, dropI, addSuperUser,
	}

	return xwg.NewXshell("gwshell", arr...)
}

var GwShell = &cobra.Command{
	Use:   "shell",
	Short: "start gw shell",
	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, cfg.ServiceGW.Name, misc.PathLog, misc.Format
		//初始化logger
		xlog.InitLogger(format, cmd.Use, level, name, path)

		//初始化mysql 和 redis
		global.InitRedis(0)

		//检查influx数据库链接是否 ok
		global.InitInflux()

		dbName := cfg.Postgres.DBName
		// 检查数据库是否存在，如果不存在，则创建数据库
		_ = global.CheckPGDBExisted(dbName)

		// 初始化数据库的连接
		global.InitPostgres(dbName)

		xlog.Debug("------------------------ 1")
	},
	Run: func(cmd *cobra.Command, args []string) {
		// cfg := config.Get()

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		wg.Go(NewShell())
		wg.Wait()

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}
