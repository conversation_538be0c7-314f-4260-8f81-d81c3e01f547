package cmd

import (
	"fmt"
	"os"
	"time"

	"github.com/spf13/cobra"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/shui206"
	"bs.com/ebox/pkg/szy206"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xwg"
)

// 水资源 206/427
var Shui206 = &cobra.Command{
	Use:   "shui206",
	Short: "start szy206 server",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		slCfg := config.Get().ServiceSzy206
		misc := config.Get().Misc

		// 如果容器环境变量设置了 AppName，则使用此 AppName 作为此实例的 name
		app_name := os.Getenv("AppName")
		if app_name != "" {
			slCfg.Name = app_name
		}

		//初始化logger
		level, name, path, format := misc.LogLevel, slCfg.Name, misc.PathLog, misc.Format

		//初始化logger
		xlog.InitLogger(format, cmd.Use, level, name, path)

		global.CheckLicense() //检查 license 信息

		szy206.SetProtocalVersion(global.P_SZY206)
	},
	Run: func(cmd *cobra.Command, args []string) {

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		//运行 shell
		wg.Go(shui206.NewSlShell())

		//运行 tcp server
		wg.Go(shui206.NewServerShui(global.P_SZY206))

		wg.Wait()

		time.Sleep(100 * time.Millisecond)
		fmt.Println("by")

	},
}
