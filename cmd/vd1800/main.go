package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/pkg/xutils"
	"bs.com/ebox/pkg/xwg"

	vd "bs.com/ebox/internal/vd1800"
)

func main() {

	executablePath, err := os.Executable()
	if err != nil {
		fmt.Println("get executable path error: ", err)
		os.Exit(1)
	}
	fmt.Println("executable path: ", executablePath)
	// configFile := "config.yaml"

	//cobro的flag ，仅能用于子命令，因为没有parse的这个过程
	// rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "config.yaml", "select configuration file")
	// rootCmd.Flags().StringVarP(&configFile, "config", "c", "config.yaml", "select configuration file")

	var configFile string
	var vdFile string
	flag.StringVar(&configFile, "config", "config.yaml", "select configuration file")
	flag.StringVar(&vdFile, "vd", "vd_nc1800.json", "select configuration file")
	flag.Parse()

	fmt.Println("we use config file : ", configFile)

	//检查配置文件
	if _, err := os.Stat(configFile); err != nil {
		configFile = filepath.Join(xutils.GetExecPath(), configFile)
		if _, err = os.Stat(configFile); err != nil {
			fmt.Println("config file not found: ", configFile)
			os.Exit(1)
		}
	}

	// 检查虚拟设备描述文件
	if _, err := os.Stat(vdFile); err != nil {
		vdFile = filepath.Join(xutils.GetExecPath(), vdFile)
		if _, err = os.Stat(vdFile); err != nil {
			fmt.Println("config file not found: ", vdFile)
			os.Exit(1)
		}
	}

	//解析配置文件
	config.ParseConfigForVd(configFile)

	wg := xwg.NewWorkerGroup()

	//监听信号
	wg.Go(xwg.NewSignalServer())

	//运行 shell
	wg.Go(vd.NewVdeviceShell())

	//运行 vd client
	wg.Go(vd.NewServerVD(vdFile))

	wg.Wait()

	time.Sleep(100 * time.Millisecond)
	fmt.Println("by")

}
