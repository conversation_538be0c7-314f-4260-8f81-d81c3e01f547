package cmd

import (
	"fmt"
	"time"

	"bs.com/ebox/internal/udps"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xwg"
	"github.com/spf13/cobra"
)

var UDPS = &cobra.Command{
	Use:   "udps",
	Short: "start demo udp server",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		//初始化logger
		level, name, path, format := "debug", "udps", "./", "text"

		//初始化logger
		xlog.InitLogger(format, name, level, name, path)
	},
	Run: func(cmd *cobra.Command, args []string) {

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		//运行 udp server
		wg.Go(udps.NewServerUDP())

		wg.Wait()

		time.Sleep(100 * time.Millisecond)
		fmt.Println("by")
	},
}
