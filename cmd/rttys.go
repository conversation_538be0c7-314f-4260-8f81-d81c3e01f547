package cmd

import (
	"fmt"
	"time"

	"bs.com/ebox/pkg/xwg"
	"github.com/spf13/cobra"

	"bs.com/ebox/internal/rttys"
)

var Rttys = &cobra.Command{
	Use:   "rttys",
	Short: "start rtty-server",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		//init
	},
	Run: func(cmd *cobra.Command, args []string) {

		wg := xwg.NewWorkerGroup()

		wg.Go(xwg.NewSignalServer()) //监听信号
		wg.Go(rttys.NewRttyServer()) //运行 rtty server

		wg.Wait()
		time.Sleep(100 * time.Millisecond)
		fmt.Println("by")
	},
}
