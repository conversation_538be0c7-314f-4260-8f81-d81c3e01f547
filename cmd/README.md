## 介绍

可执行文件的入口。

- gw     对外提供 http 和 websocket 的服务。支持用户管理、产品管理、属性管理、固件升级、水利整编等。
    - iot  主要是RTU2800 设备接入、管理等 —— 希望可以和gw集成在一起，作为gw的一个线程启动。
    - mqtt 支持 mqtt 协议的设备接入。
- daba   大坝监测相关协议服务器。支持大坝监测网关。基于 sl2022。也做大坝监测网关设备的验证平台。
- shui   水利、水文相关，测站管理，远程数据整编下载。支持 RTU2800。
- vdmcu  模拟 RTU2800 设备。测试用。
- vdlinux 模拟大坝监测网关设备。测试用。

main.go 仅做全局配置文件解析、服务入口的调用。使用一个可执行文件，启动全部服务。

iot 服务和 shui 服务有独立的http 端口和tcp端口，可以单独运行。