package main

import (
	"fmt"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/pkg/xlog"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

// 替换gateway，作为http 服务器
var CheckAutoId = &cobra.Command{
	Use:   "checkAutoId",
	Short: "check postgresql max auto id",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, "", "", "text"
		//初始化logger
		xlog.InitLogger(format, cmd.Use, level, name, path)

		dbName := cfg.Postgres.DBName
		global.InitPostgres(dbName)

	},
	Run: func(cmd *cobra.Command, args []string) {
		checkAutoMaxid(global.DB())

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}

func checkAutoMaxid(db *gorm.DB) {
	tbNameList := []string{
		// SimCard{}.TableName(),

	}
	fmt.Println(tbNameList)
	for _, tbName := range tbNameList {
		// fmt.Println(tbName)
		sql := fmt.Sprintf("SELECT MAX(id) as mid FROM %s", tbName)
		var mid int64
		if err := db.Raw(sql).Row().Scan(&mid); err != nil {
			fmt.Sprintln("query max id error:", err)
		}
		fmt.Printf("table:%s, mid:%d \n", tbName, mid)
		if mid <= 0 {
			continue
		}
		setSql := fmt.Sprintf("SELEcT setval('%s_id_seq', %d)", tbName, mid)
		if err := db.Exec(setSql).Error; err != nil {
			fmt.Println("set val error: ", err)
		}
	}

}
