package main

import (
	"fmt"
	"os"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/gw/dto"
	"bs.com/ebox/internal/gw/model"
	"bs.com/ebox/pkg/xlog"
	"github.com/spf13/cobra"
)

/*
 * 将station 绑定的 sensormap 迁移到datapoint
 */
var StationSensorMapToDataPoint = &cobra.Command{
	Use:   "sensormap_to_datapoint",
	Short: "将station 绑定的sensormap迁移到datapoint",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, "", "", "text"
		//初始化logger
		//xlog.Init(level, "json", name, path)
		xlog.InitLogger(format, cmd.Use, level, name, path)

		dbName := cfg.Postgres.DBName
		global.InitPostgres(dbName)
	},
	Run: func(cmd *cobra.Command, args []string) {
		start()

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}

func start() {
	fmt.Println("run start ...")

	// 查询所有的 sensor_map 数据
	pi := &dto.PageInfo{Page: 0, PageSize: 100}
	pi.Check()

	session := global.DB()
	for {
		sensorMapList := []*StationSensorMap{}
		err := model.PagedFind(session, &sensorMapList, pi)
		if err != nil {
			xlog.Error("page find error:", err)
			os.Exit(1)
		}
		if len(sensorMapList) <= 0 {
			break
		}
		pi.Page += 1
		dataToDataPoint(sensorMapList)
	}

}

// TODO：此表要废弃
type StationSensorMap struct {
	ID        int64     `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time `gorm:"index;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"comment:更新时间" json:"updated_at"`

	//联合唯一索引
	StationID  int64  `gorm:"index;unique;comment:测站ID" json:"station_id"` //测站ID
	StationSN  string `gorm:"index;comment:测站编码" json:"station_sn"`        //测站编码，方便查询
	SensorEnum string `gorm:"type:varchar(64);index;unique;comment:采集要素分组" json:"sensor_enum"`
}

// 记得迁移 meta 信息
// 雨量               | 0065599953 | rain          | {"params": {}, "channel": "", "elements": [{"code": 34, "identifier": ""}, {"code": 244, "identifier": ""}]}
// 图片信息           | 0065599953 | image         | {"params": {}, "elements": [{"code": 243, "identifier": ""}]}
// 水位               | 0065599953 | water         | {"params": {}, "channel": "", "elements": [{"code": 57, "identifier": ""}, {"code": 245, "identifier": ""}]}

var stationMap map[string]*model.Station = make(map[string]*model.Station)

func dataToDataPoint(sensorMapList []*StationSensorMap) {

	arrProfile := model.List651PointProfile()
	mapProfile := make(map[string]*model.NormalProfile)
	for _, item := range arrProfile {
		mapProfile[item.PointType] = item
	}

	daoDP := model.NewDataPointRepo()
	daoSt := model.NewDaoStation()
	var err error
	for _, ssensor := range sensorMapList {
		station, ok := stationMap[ssensor.StationSN]
		if !ok {
			station, err = daoSt.StationGetByStationSN(ssensor.StationSN)
			if err != nil {
				xlog.Errorf("find station by sn error: %v", err)
				continue
			} else {
				stationMap[ssensor.StationSN] = station
			}
		}

		// SensorEnum 即 PointType
		if item, ok := mapProfile[ssensor.SensorEnum]; ok {
			datapoint := model.Datapoint{
				ProjectCode: station.ProjectCode,
				StationSN:   ssensor.StationSN,
				PointType:   item.PointType,
				Name:        item.Name, // 测点名字默认是描述信息的名字
				Lat:         station.Lat,
				Lng:         station.Lng,
				Meta: map[string]any{
					"elements": item.Elements,
				},
			}

			err := daoDP.InsertOrUpdate(&datapoint)
			if err != nil {
				xlog.Error("insert error:", err)
				continue
			} else {
				xlog.Infof("insert datapoint success:  %s", ssensor.StationSN)
			}
		}
	}
}
