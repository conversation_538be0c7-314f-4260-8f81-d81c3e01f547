/**
 *
 *    ____          __
 *   / __/__ ____ _/ /__
 *  / _// _ `/ _ `/ / -_)
 * /___/\_,_/\_, /_/\__/
 *         /___/
 *
 *
 * generate by http://patorjk.com/software/taag/#p=display&f=Small%20Slant&t=Eagle
 */
package main

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/cobra"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/pkg/xutils"
)

// 使用编译时自动替代
// var Version string
// var CommitID string
// var BuildTime string
var rootCmd = &cobra.Command{
	Use:   "ebox",
	Short: "ebox iot platform",
	Long:  "service : gateway ",
	Args: func(cmd *cobra.Command, args []string) error {
		if len(args) < 1 {
			_ = cmd.Usage()
			return errors.New("show usage")
		}
		return nil
	},
}

func main() {
	//cobro的flag ，仅能用于子命令，因为没有parse的这个过程
	//rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "config.yaml", "select configuration file")
	//rootCmd.Flags().StringVarP(&configFile, "config", "c", "config.yaml", "select configuration file")

	//使用原生的flag库
	//usage ./ebox_darwin.bin --config config_dev.yaml gw
	configFile := "config.yaml"
	fmt.Println("we use config file : ", configFile)

	//检查配置文件
	if _, err := os.Stat(configFile); err != nil {
		configFile = filepath.Join(xutils.GetExecPath(), configFile)
		if _, err = os.Stat(configFile); err != nil {
			fmt.Println("config file not found: ", configFile)
			os.Exit(1)
		}
	}

	//全局容器
	global.InitGlobal()

	//解析配置文件
	config.ParseConfig(configFile)

	//config.InitEagleCfg(".") // 需要指定目录

	//添加命令行入口
	rootCmd.AddCommand(CheckAutoId)
	rootCmd.AddCommand(StationBandImei)
	rootCmd.AddCommand(SensorParamFromArrayToMap)
	rootCmd.AddCommand(StationSensorMapToDataPoint)

	rootCmd.AddCommand(tableExport)
	rootCmd.AddCommand(tableImport)
	rootCmd.AddCommand(tableRebuild)

	rootCmd.AddCommand(tableSync)

	//
	rootCmd.AddCommand(getGeoJson)
	_ = rootCmd.Execute()
}
