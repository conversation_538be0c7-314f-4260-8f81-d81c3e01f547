package main

import (
	"fmt"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/pkg/xlog"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

// 替换gateway，作为http 服务器
var StationBandImei = &cobra.Command{
	Use:   "imei_2_station",
	Short: "将线上的dev的imei绑定到station表",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, "", "", "text"
		//初始化logger
		xlog.InitLogger(format, cmd.Use, level, name, path)

		dbName := cfg.Postgres.DBName
		global.InitPostgres(dbName)
	},
	Run: func(cmd *cobra.Command, args []string) {

		// 3.删除不必要的列（必须在删除测站的后面）
		DelNotRequireColumnAndIndex(global.DB())

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}

func delColumn(db *gorm.DB, tbname string, delColumns []string) {
	for _, column := range delColumns {
		sql := fmt.Sprintf("ALTER TABLE %s DROP COLUMN %s", tbname, column)
		if err := db.Exec(sql).Error; err != nil {
			fmt.Printf("Failed to drop column: %s, error: %s \n", column, err.Error())
		}
	}
}

func DelNotRequireColumnAndIndex(db *gorm.DB) {
	// 删除测站不需要的列, 对应的索引和约束会同时删除
	delDevColumns := []string{"station_sn", "is_activated", "activated_at"}
	delColumn(db, "device", delDevColumns)

	// 删除测站不需要的列, 索引和约束会同时删除
	delStationColumns := []string{"is_activated", "activated_at"}
	delColumn(db, "station", delStationColumns)

}
