package main

import (
	"fmt"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/gw/dto"
	"bs.com/ebox/internal/gw/model"
	"bs.com/ebox/pkg/xlog"
	"github.com/spf13/cobra"
	"gorm.io/datatypes"
)

/*
 * sensor 数据库 的 params 从 array转为 map
 */
var SensorParamFromArrayToMap = &cobra.Command{
	Use:   "sensor_param",
	Short: "将数据库中的params从array转为map",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, "", "", "text"
		//初始化logger
		//xlog.Init(level, "json", name, path)
		xlog.InitLogger(format, cmd.Use, level, name, path)

		dbName := cfg.Postgres.DBName
		global.InitPostgres(dbName)
	},
	Run: func(cmd *cobra.Command, args []string) {
		todoQuery()

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}

type TmpSensor struct {
	ID        int64     `gorm:"primaryKey" json:"id"`
	UpdatedAt time.Time `gorm:"comment:更新时间" json:"updated_at"`

	DriverID int64 `gorm:"uniqueIndex;comment:驱动ID"  json:"driver_id"` //驱动 ID

	SN            int64                          `gorm:"uniqueIndex;comment:编号"  json:"sn"`                         //同一个驱动支持的传感器，需保证驱动 ID-传感器 SN 的联合唯一性
	Name          string                         `gorm:"type:varchar(64);not null;comment:名字"  json:"name"`         //厂家-型号
	Status        bool                           `gorm:"index;default:true;comment:状态" json:"status"`               //false:停用， true：正常
	Params        datatypes.JSONSlice[dto.Param] `gorm:"comment:传感器参数" json:"params"`                               //对应 ParamTemplate 的模板参数值，基于驱动中的模板参数描述。可以为空
	Desc          string                         `gorm:"type:varchar(255);comment:备注"  json:"desc"`                 //传感器描述
	ProtocFileUrl string                         `gorm:"type:varchar(500);comment:协议文件连接"   json:"protoc_file_url"` // 协议文件连接
}

func todoQuery() {
	db := global.DB()
	session := db.Model(&TmpSensor{})
	session = session.Order("sn ASC")
	var arr []*TmpSensor
	pinfo := &dto.PageInfo{
		Page:     1,
		PageSize: 10,
	}

	for {
		err := model.PagedFind(session, &arr, pinfo)
		if err != nil {
			xlog.Info(fmt.Sprintf("err:%v", err.Error()))
		}
		changeParams(arr)
		if len(arr) <= 0 {
			break
		} else {
			pinfo.Page = pinfo.Page + 1
		}
	}
}

func changeParams(arr []*TmpSensor) {
	for _, item := range arr {
		// fmt.Println(item.Name, item)
		req := dto.ReqSensorUpdate{
			ID:       item.ID,
			DriverID: item.DriverID,
			Name:     item.Name,
			SN:       item.SN,
			Status:   item.Status,

			Desc:          item.Desc,
			ProtocFileUrl: item.ProtocFileUrl,
		}
		tmpMap := make(map[string]interface{})
		for _, param := range item.Params {
			fmt.Println(param.Name, param)
			tmpMap[param.Name] = param
		}
		req.Params = datatypes.JSONMap(tmpMap)
		fmt.Println("params:", req.Params)
		data := model.StructToMap(req)
		data["params"] = datatypes.JSONMap(tmpMap)
		if err := global.G.DB.Model(&model.Sensor{}).Where("id=?", req.ID).Updates(data).Error; err != nil {
			xlog.Error("db update err", err)
		}
	}
}
