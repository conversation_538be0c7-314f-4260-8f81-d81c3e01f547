package main

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/http/cookiejar"
	"path/filepath"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/gw/model"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xutils"
	"github.com/spf13/cobra"
)

// 替换gateway，作为http 服务器
var getGeoJson = &cobra.Command{
	Use:   "get_geo_json",
	Short: "获取区域表的geojson",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, "", "", "text"
		xlog.InitLogger(format, cmd.Use, level, name, path)

		dbName := cfg.Postgres.DBName
		global.InitPostgres(dbName)
	},
	Run: func(cmd *cobra.Command, args []string) {

		getDbAreaCodeAndGetGeoJson()

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}

func getDbAreaCodeAndGetGeoJson() {
	sys := model.NewDaoSystem()
	levels := []int{0, 1, 2, 3, 4, 5}
	for l, _ := range levels {
		areaList, err := sys.GetAreaListByLevel(l)
		if err != nil {
			fmt.Println("err:", err)
			continue
		}
		if len(areaList) <= 0 {
			continue
		}
		for _, aitem := range areaList {
			getAreaCodeGeoJons(aitem.AreaCode)
			time.Sleep(1 * time.Second)
		}
	}

}

func getAreaCodeGeoJons(areaCode string) {

	fmt.Println("cur area code:", areaCode)

	geoURL := fmt.Sprintf("https://geo.datav.aliyun.com/areas_v3/bound/%s.json", areaCode)

	//跳过证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	// http cookie接口
	cookieJar, _ := cookiejar.New(nil)
	httpClient := &http.Client{
		Jar:       cookieJar,
		Transport: tr,
	}

	resp, err := httpClient.Get(geoURL)
	if err != nil {
		xlog.Errorf(" error:%v", err)

		return
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		xlog.Errorf("  error:%v", err)

		return
	}
	var data map[string]interface{}
	err = json.Unmarshal(body, &data)
	if err != nil {
		xlog.Errorf("  error:%v", err)
		return
	}
	jsonPath := "geojson"
	xutils.MkDirIfNotExist(jsonPath)
	curfilename := filepath.Join(jsonPath, areaCode+".json")
	err = ioutil.WriteFile(curfilename, body, 0644)
	if err != nil {
		fmt.Println("写文件失败")
		return
	}
}
