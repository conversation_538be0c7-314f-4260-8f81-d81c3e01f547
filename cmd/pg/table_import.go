package main

import (
	"fmt"
	"log"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/gw/model"
	"bs.com/ebox/pkg/xlog"
	"github.com/spf13/cobra"
)

var tableImport = &cobra.Command{
	Use:   "import",
	Short: "将 json 导入到对应的数据库表",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, "", "", "text"
		//初始化logger
		xlog.InitLogger(format, cmd.Use, level, name, path)

		dbName := cfg.Postgres.DBName
		global.InitPostgres(dbName)
	},
	Run: func(cmd *cobra.Command, args []string) {

		// 3.删除不必要的列（必须在删除测站的后面）
		db := global.DB()

		arr := []any{
			// model.Product{},
			// model.Area{},
			model.Resource{},
			// model.Driver{},
			// model.Sensor{},
			// model.DictGroup{},
			// model.DictItem{},
		}

		// 导入用户和产品表数据
		for _, one := range arr {
			tableName := model.GetTableName(one)
			if err := model.ImportTableFromJson(db, one); err != nil {
				log.Fatalf("failed to import table:%s, err: %v", tableName, err)
			}
		}

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}
