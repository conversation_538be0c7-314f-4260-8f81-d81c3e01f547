package main

import (
	"fmt"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/pkg/xlog"
	"github.com/spf13/cobra"
)

var tableRebuild = &cobra.Command{
	Use:   "rebuild",
	Short: "重建约束、索引、seq_id",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, "", "", "text"
		//初始化logger
		xlog.InitLogger(format, cmd.Use, level, name, path)

		dbName := cfg.Postgres.DBName
		global.InitPostgres(dbName)
	},
	Run: func(cmd *cobra.Command, args []string) {
		// 重建表的 sequence id 表

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}

/*
CREATE SEQUENCE <t_name>_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 设置表的 id 列使用 sequence
ALTER TABLE <t_name> ALTER COLUMN id SET DEFAULT nextval('<t_name>_id_seq');

-- 同步 sequence 的值与表中最大 id 值
SELECT setval('<t_name>_id_seq', COALESCE((SELECT MAX(id) FROM <t_name>), 0) + 1, false);
*/
