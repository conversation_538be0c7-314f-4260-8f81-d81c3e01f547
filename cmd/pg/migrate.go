package main

import (
	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/internal/gw/model"
	"bs.com/ebox/pkg/xlog"
	"github.com/spf13/cobra"
)

// 添加了某些字段，需要更新填充列

var tableSync = &cobra.Command{
	Use:   "sync",
	Short: "同步设备表和测站表",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger
		level, name, path, format := misc.LogLevel, "", "", "text"
		//初始化logger
		xlog.InitLogger(format, cmd.Use, level, name, path)

		dbName := cfg.Postgres.DBName
		global.InitPostgres(dbName)
	},
	Run: func(cmd *cobra.Command, args []string) {
		daoStation := model.NewDaoStation()
		_ = daoStation.MigrateStation()

		daoDevice := model.NewDaoIOT()
		_ = daoDevice.MigrateDevice()
	},
}
