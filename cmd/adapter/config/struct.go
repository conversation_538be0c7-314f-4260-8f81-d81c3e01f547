package config

type Config struct {
	Mqtt mqttConfig `json:"mqtt"       yaml:"mqtt"` //mqtt
	DB   dbConfig   `json:"db"         yaml:"db"`   //数据库
	Misc miscConfig `json:"misc"       yaml:"misc"` //其他
}

type dbConfig struct {
	Host     string `json:"host"`     //*************
	Port     uint16 `json:"port"`     //31433
	Username string `json:"username"` //sa
	Password string `json:"password"` //zhdgps_123456
	Database string `json:"database"` //adapter
}

// TODO：部署的时候，不要暴漏 mqtt 的相关信息
type mqttConfig struct {
	BrokerHost string `json:"broker_host"         yaml:"broker_host"`
	// HeartbeatTimeout int    `json:"heartbeat_timeout"   yaml:"heartbeat_timeout"`
}

type miscConfig struct {
	MachineID string `json:"machine_id"                yaml:"machine_id"` //机器唯一固定 ID
	AreaCode  string `json:"area_code"                 yaml:"area_code"`  //区域编码
	HttpHost  string `json:"http_host"                 yaml:"http_host"`  //测站列表文件，一行一个测站编码
}
