package config

import (
	"bs.com/ebox/pkg/xlog"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
)

var cfg *Config

func Get() *Config {
	return cfg
}

func ParseConfig(filename string) error {
	xlog.Info("parse config file : ", filename)
	viper.SetConfigFile(filename)
	viper.AddConfigPath(".")
	err := viper.ReadInConfig()
	if err != nil {
		return err
	}

	cfg = &Config{}
	return viper.Unmarshal(cfg, func(config *mapstructure.DecoderConfig) {
		config.TagName = "yaml"
	})
}
