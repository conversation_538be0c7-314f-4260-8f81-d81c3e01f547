package main

import (
	"flag"
	"fmt"
	"os"
	"os/user"
	"path/filepath"
	"runtime"
	"time"

	"bs.com/ebox/cmd/adapter/config"
	"bs.com/ebox/cmd/adapter/model"
	"bs.com/ebox/cmd/adapter/mqxx"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xutils"
	"bs.com/ebox/pkg/xwg"
	"github.com/abiosoft/ishell/v2"
)

func NewShell(name string) xwg.IService {
	hi := &ishell.Cmd{
		Name: "hi",
		Help: "hi",
		Func: func(c *ishell.Context) {
			c.Println("hello: ", time.Now().String())
		},
	}

	read := &ishell.Cmd{
		Name: "read",
		Help: "read table limit 10",
		Func: func(c *ishell.Context) {
			if len(c.Args) != 1 {
				c.Println("read [table name]")
				return
			}
			data, err := model.ReadTable(c.Args[0])
			if err != nil {
				c.Println("read table err :", err.Error())
				return
			}
			c.Println("data: ", xutils.JsonStringIndent(data))
		},
	}

	//手动执行 auto migrate，在
	mig := &ishell.Cmd{
		Name: "auto_mig",
		Help: "auto migrate",
		Func: func(c *ishell.Context) {
			_ = model.MSSQLAutoMigrate()
		},
	}

	cmdArr := []*ishell.Cmd{hi, read, mig}
	return xwg.NewXshell(name, cmdArr...)
}

func getMachinaHostname() string {
	hostname, err := os.Hostname() //2、其次使用系统的主机名
	if err != nil {
		return getCurrentUsername()
	}
	return hostname
}

func getCurrentUsername() string {
	user, err := user.Current() //3、再次，使用当前用户名
	if err != nil {
		return runtime.GOOS //4、最后，使用当前系统类型
	}
	return user.Username
}

func main() {
	xlog.SetLevel("debug")
	machine := getMachinaHostname()
	xlog.Info("=========== ", machine)

	var configFile string
	var tableName string
	var autoMig bool
	flag.StringVar(&configFile, "config", "config.yaml", "select configuration file")
	flag.StringVar(&tableName, "table", "", "select one table to dump")
	flag.BoolVar(&autoMig, "mig", false, "auto migrate ,default false")
	flag.Parse()

	xlog.Info("config file : ", configFile)
	xlog.Info("dump table:", tableName)

	//检查配置文件
	if _, err := os.Stat(configFile); err != nil {
		configFile = filepath.Join(xutils.GetExecPath(), configFile)
		if _, err = os.Stat(configFile); err != nil {
			fmt.Println("config file not found: ", configFile)
			os.Exit(0)
		}
	}

	//解析配置文件
	if err := config.ParseConfig(configFile); err != nil {
		xlog.Error("parse config error")
		os.Exit(0)
	}

	//初始化数据库链接
	if err := model.InitAdapterDB(); err != nil {
		xlog.Error("init db err: ", err)
		os.Exit(0)
	}

	if autoMig {
		xlog.Info(">>>>>> do auto migrate")
		err := model.MSSQLAutoMigrate()
		if err != nil {
			xlog.Error("auto migrate err:", err)
			os.Exit(0)
		} else {
			xlog.Info("auto migrate OK")
		}
	}

	//测试用
	if tableName != "" {
		ret, err := model.ReadTable(tableName)
		if err != nil {
			xlog.Error("read failed:", err)
			return
		}
		xlog.Info(xutils.JSONString(ret))
		return
	}

	//start services
	wg := xwg.NewWorkerGroup()

	//监听信号
	wg.Go(xwg.NewSignalServer())

	//运行 shell
	wg.Go(NewShell(config.Get().Misc.MachineID))

	//mqtt
	wg.Go(mqxx.NewMqttAdapter())

	wg.Wait()

	time.Sleep(200 * time.Millisecond) //200ms
	fmt.Println("by")
}
