package mqxx

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"bs.com/ebox/cmd/adapter/config"
	"bs.com/ebox/cmd/adapter/model"
	"bs.com/ebox/pkg/bean"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xmqtt"
	"bs.com/ebox/pkg/xutils"
	"bs.com/ebox/pkg/xwg"
)

type Adapter struct {
	mqttHost     string
	mqttClientID string

	mqtt *xmqtt.MqttClient

	*model.Dao

	mStationCategory sync.Map //测站类型码
}

func NewMqttAdapter() xwg.IService {
	cfg := config.Get().Mqtt
	brokerHost := cfg.BrokerHost
	if brokerHost == "" {
		//默认使用线上的 mqtt broker
		brokerHost = "*************:51883"
	}

	clientID := config.Get().Misc.MachineID
	if clientID == "" {
		xlog.Error("invalid machine id")
		os.Exit(0)
	}

	// 每次启动都是随机值
	clientID = clientID + "-" + xutils.RandStringN(10)

	a := &Adapter{
		mqttHost:     brokerHost,
		mqttClientID: clientID,
	}

	a.Dao = model.NewDao()

	return a
}

// 如果返回错误，则关闭所有其他协程
func (a *Adapter) Name() string {
	return a.mqttClientID
}

// mqtt 开启之前，拉去最新的测站列表，然后订阅
// TODO：如果订阅基于 areaCode ，就 nice 了。但是 sl651 服务并没有去查询 gw 获取到测站所在 areaCode，所以用这种方式。
func (a *Adapter) Run(ctx context.Context) error {
	defer func() {
		if a.mqtt != nil {
			a.mqtt.Stop()
		}
		xutils.Recovery("mqtt adapter")
	}()

	xlog.Info("start mqtt adapter", "host", a.mqttHost, "clientID", a.mqttClientID)

	a.mqtt = xmqtt.NewMqttClient(a.mqttHost, a.mqttClientID)

	//ssnArr, err := parseStatonSN()
	stArr, err := listStationRealtime()
	if err != nil || len(stArr) == 0 {
		xlog.Error("parse station list file err:" + err.Error())
		return err
	}

	//开机，订阅全部测站的数据
	for _, one := range stArr {
		xlog.Info("sub station, and upsert : ", xutils.JSONString(one))

		subTopic := fmt.Sprintf("/shui651/up/%s", one.StationSN)
		a.mqtt.Handle(subTopic, a.MqttRecv) //下行消息接收

		one.StationSN = strings.TrimSpace(one.StationSN) //去掉空格

		err = a.StationInfoUpsert(one)
		if err != nil {
			xlog.Warn("station info upsert err:" + err.Error())
		}
	}

	// 订阅消息：用于云服务器主动与adapter 同步测站信息
	// topic 最后一个字段是xx县的区域编码
	areaCode := config.Get().Misc.AreaCode
	stTopic := fmt.Sprintf("/adapter/manage/%s", areaCode)
	a.mqtt.Handle(stTopic, a.MqttRecvManager) //下行消息接收

	err = a.mqtt.Start("", "")
	if err != nil {
		xlog.Error("start adapter mqtt client err: " + err.Error())
		return err
	}

	// 间隔 1 个小时，去拉取一次测站列表
	go func() {
		if p := recover(); p != nil {
			xlog.Error("pullSyncStationList failed", "err", err.Error())
		}
		time.Sleep(1 * time.Hour)

		a.pullSyncStationList()
	}()

	<-ctx.Done()

	return nil
}

// 通过定时拉取的方式，同步测站信息
func (a *Adapter) pullSyncStationList() error {
	//ssnArr, err := parseStatonSN()
	stArr, err := listStationRealtime()
	if err != nil || len(stArr) == 0 {
		xlog.Error("parse station list file err:" + err.Error())
		return err
	}

	//订阅这些测站的数据
	for _, one := range stArr {
		xlog.Info("sub station, and upsert : ", xutils.JSONString(one))
		one.StationSN = strings.TrimSpace(one.StationSN) //去掉空格

		if a.HasSTBPRP(one.StationSN) == false {
			// 测站不存在，说明是新的，则订阅消息
			subTopic := fmt.Sprintf("/shui651/up/%s", one.StationSN)
			a.mqtt.Handle(subTopic, a.MqttRecv) //下行消息接收
		}

		// 新的添加，旧的更新
		err = a.StationInfoUpsert(one)
		if err != nil {
			xlog.Warn("station info upsert err:" + err.Error())
		}
	}

	return nil
}

// ==========================================================================  测站管理
func (a *Adapter) MqttRecvManager(topic string, data []byte) error {
	xlog.Infof("topic: %s ,data: %s", topic, string(data))
	var msg bean.MqttMessage
	err := json.Unmarshal(data, &msg)
	if err != nil {
		xlog.Error("unmarshal failed : ", err)
		return err
	}
	switch msg.CMD {
	case "station": //添加或者更新测站基本信息表
		st := &bean.StationMeta{}
		err = json.Unmarshal(data, st)
		if err != nil {
			xlog.Error("report data unmarshal err: ", err)
			return err
		}

		st.StationSN = strings.TrimSpace(st.StationSN) //去掉空格

		// 测站不存在，说明是新的，则订阅消息
		if a.HasSTBPRP(st.StationSN) == false {
			subTopic := fmt.Sprintf("/shui651/up/%s", st.StationSN)
			a.mqtt.Handle(subTopic, a.MqttRecv) //下行消息接收
		}

		// 更新到数据库
		err = a.StationInfoUpsert(st)
		if err != nil {
			xlog.Error("upsert station err:", err)
			return err
		}

	default:
		xlog.Warn("not support mt: " + msg.CMD)
	}

	return err
}

// ==========================================================================  接收测站数据
func (a *Adapter) MqttRecv(topic string, data []byte) error {
	xlog.Infof("topic: %s ,data: %s", topic, string(data))

	var msg bean.MqttMessage
	err := json.Unmarshal(data, &msg)
	if err != nil {
		xlog.Error("unmarshal failed : " + err.Error())
		return err
	}

	if msg.CMD == bean.MtFault {
		xlog.Warn("[ignore] msg fault", "msg", msg)
		return nil
	}

	//收到数据，存储到数据库
	switch msg.CMD {
	case bean.MtReport:
		err = a.Report(msg.Body)

	default:
		xlog.Warn("not support mt: " + msg.CMD)
	}

	return err

}

// send by mqtt
func (a *Adapter) MqttSend(ssn, mt string, data interface{}) error {
	xlog.Infof("send message, mt %s, data:%s", mt, xutils.JSONString(data))

	buf, _ := json.Marshal(data)

	msg := &bean.MqttMessage{
		CMD:  mt,
		Body: buf,
	}

	pubTopic := fmt.Sprintf("/adapter/up/%s", ssn)
	return a.mqtt.Pub(pubTopic, msg)
}
