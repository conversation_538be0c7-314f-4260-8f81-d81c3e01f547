package mqxx

import (
	"strings"
	"time"

	"bs.com/ebox/cmd/adapter/model"
	"bs.com/ebox/pkg/xlog"
)

// 插入的数据存储到数据库
func (a *Adapter) AddSamples(ssn, sensor string, data []*Sl651Data) error {
	xlog.Infof("add sample,ssn :%s, sensor:%s", ssn, sensor)
	var err error

	// 从缓存里面得到测站类型码
	val, ok1 := a.mStationCategory.Load(ssn)
	if !ok1 {
		xlog.Error("water has no cate...")
		return nil
	}

	cate, ok2 := val.(string)
	if !ok2 {
		xlog.Error("water cate map invalid val")
		return nil
	}

	// TODO:应该从测站信息里面解析有哪些测点。没有定义测点的话就不存储
	ssn = strings.TrimSpace(ssn) //去掉空格
	ssn = ssn[2:]                //截取后面的 8 位
	for _, one := range data {
		switch sensor {
		case "rain":
			err = a.AddPPTN(&model.ST_PPTN_R{
				STCD: ssn,
				TM:   time.Unix(one.Timestamp, 0),
				DRP:  one.ValueNumber,
				INTV: 0.05,
				FLAG: 0,
			})

		case "water":
			if cate == "48H" {
				//河道
				err = a.AddRIVER(&model.ST_RIVER_R{
					STCD: ssn,
					TM:   time.Unix(one.Timestamp, 0),
					Z:    one.ValueNumber,
				})
			} else if cate == "4BH" {
				//水库湖泊
				err = a.AddRSVR(&model.ST_RSVR_R{
					STCD: ssn,
					TM:   time.Unix(one.Timestamp, 0),
					RZ:   one.ValueNumber,
				})
			} else {
				xlog.Error("water invalid category:", cate)
			}

		case "image":
			err = a.AddIMGINFO(&model.ST_IMGINFO_R{
				STCD: ssn,
				TM:   time.Unix(one.Timestamp, 0),
				VTDT: one.ValueString,
			})
		case "battery":
			err = a.AddVOLTAGE(&model.ST_VOLTAGE_R{
				STCD: ssn,
				TM:   time.Unix(one.Timestamp, 0),
				V:    one.ValueNumber,
			})
		default:
		}
	}
	return err
}

func (a *Adapter) FN34AddRain(ssn string, data []*Sl651Data) error {
	xlog.Infof("add sample,ssn :%s, sensor:%s", ssn, "rain")
	var err error

	ssn = strings.TrimSpace(ssn) //去掉空格
	ssn = ssn[2:]                //截取后面的 8 位
	for _, one := range data {
		err = a.AddPPTN(&model.ST_PPTN_R{
			STCD: ssn,
			TM:   time.Unix(one.Timestamp, 0),
			DRP:  one.ValueNumber,
			INTV: 0.05, //5 分钟雨量
			FLAG: 0,
		})
	}
	return err
}
