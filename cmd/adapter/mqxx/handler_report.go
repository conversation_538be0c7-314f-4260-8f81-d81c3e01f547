package mqxx

import (
	"encoding/json"

	"bs.com/ebox/pkg/bean"
	"bs.com/ebox/pkg/sl651"
	"bs.com/ebox/pkg/xlog"
)

// 时序数据,返回用
type Sl651Data struct {
	Timestamp   int64   `form:"timestamp"          json:"timestamp"`    //时间戳，单位秒
	ValueNumber float64 `form:"value_number"       json:"value_number"` //数据内容
	ValueString string  `form:"value_string"       json:"value_string"` //数据内容
}

// 测站 类型映射
// 处理 report 消息
// report 消息来自水利服务器，带有雨量 水位 电压等传感器信息
func (a *Adapter) Report(data json.RawMessage) error {
	msg := &bean.ReportData{}
	err := json.Unmarshal(data, msg)
	if err != nil {
		xlog.Error("report data unmarshal failed")
		return err
	}

	ssn := msg.StationSN

	xlog.Infof("handler report, SSN: %s, FN: %s", ssn, sl651.MapFn[msg.FN])

	switch msg.FN {
	// FN31 uint8 = 0x31 // 均匀时段水文信息报：报送等时间间隔数据
	case sl651.FN31:
		err = a.handleFN31(ssn, msg.FN31)

	// FN32 uint8 = 0x32 // 遥测站定时报：报送由时间触发的实时数据，默认定时时间是 5 分钟
	case sl651.FN32:
		err = a.handleFN32(ssn, msg.FN32)

	// FN33 uint8 = 0x33 // 遥测站加报：报送由时间或事件触发的加报实时数据，被测要素达到设定加报阈值，遥测站向中心站报送实时信息、遥测站状态及报警信息
	case sl651.FN33:
		err = a.handleFN33(ssn, msg.FN33)

	// FN34 uint8 = 0x34 // 遥测站小时报：报送以小时为基本单位的历史数据和实时数据
	case sl651.FN34:
		err = a.handleFN34(ssn, msg.FN34)

	case sl651.FN36:
		err = a.handleN36(ssn, msg.FN36)

	default:
		xlog.Debug("not support")
	}

	if err != nil {
		xlog.Errorf("adapter report processor err: %s", err.Error())
	}
	return err
}

//============================================================================================
//注意时间步长都是默认 5分钟

// ts : 观测最末时间
// n ： 采样数据组数
func endTime5minute(ts int64, n int) int64 {
	ts = ts - int64(n*300) + 150 //取观测最初时间。如果是 11:00送上来的小时报，有 12个采样。则应该从 10:00 开始计算，则第一个 5 分钟时段数据的时间是 10:05
	return ts - ts%300 + 300     //间隔5分钟，四舍五入。加上第一个数据的 5 分钟偏移
}

// 处理上报消息，保存到数据库
func (a *Adapter) handleFN31(ssn string, data *bean.ReportFN31) (err error) {
	//step默认是5分钟
	xlog.Debug("handle fn31 report,step : ", data.Step)

	//雨量数据
	if len(data.RainSample) > 0 {
		n := len(data.RainSample)
		start := endTime5minute(data.RainSampleTimestamp, n)

		var samples []*Sl651Data
		for i, one := range data.RainSample {
			samples = append(samples, &Sl651Data{
				ValueNumber: one,
				Timestamp:   start + int64(i*300),
			})
		}

		err = a.AddSamples(ssn, "rain", samples)
	}

	//水位数据
	if len(data.WaterSample) > 0 {
		//先处理timestamp，到整点整5分
		n := len(data.WaterSample)
		start := endTime5minute(data.WaterSampleTimestamp, n)

		var samples []*Sl651Data
		for i, one := range data.WaterSample {
			samples = append(samples, &Sl651Data{
				ValueNumber: one,
				Timestamp:   start + int64(i*300),
			})
		}
		err = a.AddSamples(ssn, "water", samples)
	}

	return err
}

// 定时报
func (a *Adapter) handleFN32(ssn string, data *bean.ReportFN32) (err error) {
	//FN33的雨量是累计雨量。所以仅保存水位
	xlog.Info("FN32 water")
	//先处理timestamp，到整点整5分
	start := endTime5minute(data.Timestamp, 1)

	if data.Rain5M > 0 {
		sample := &Sl651Data{
			ValueNumber: data.Rain5M,
			Timestamp:   start,
		}
		err = a.AddSamples(ssn, "rain", []*Sl651Data{sample})
	}

	sample := &Sl651Data{
		ValueNumber: data.WaterCurrent,
		Timestamp:   start,
	}
	err = a.AddSamples(ssn, "water", []*Sl651Data{sample})

	return
}

// 加报
func (a *Adapter) handleFN33(ssn string, data *bean.ReportFN33) (err error) {
	//FN33的雨量是累计雨量。所以仅保存水位
	xlog.Info("FN33 water")
	//先处理timestamp，到整点整5分
	start := endTime5minute(data.Timestamp, 1)

	if data.Rain5M > 0 {
		sample := &Sl651Data{
			ValueNumber: data.Rain5M,
			Timestamp:   start,
		}
		err = a.AddSamples(ssn, "rain", []*Sl651Data{sample})
	}

	sample := &Sl651Data{
		ValueNumber: data.WaterCurrent,
		Timestamp:   start,
	}
	err = a.AddSamples(ssn, "water", []*Sl651Data{sample})

	return
}

// FN34 遥测站小时报
func (a *Adapter) handleFN34(ssn string, data *bean.ReportFN34) (err error) {
	xlog.Debug("handle fn34 report : ", ssn)

	//雨量数据
	if len(data.RainSample) > 0 {
		xlog.Info("FN34 rain")
		n := len(data.RainSample)
		start := endTime5minute(data.RainSampleTimestamp, n)

		var samples []*Sl651Data
		for i, one := range data.RainSample {
			samples = append(samples, &Sl651Data{
				ValueNumber: one,
				Timestamp:   start + int64(i*300),
			})
		}
		// err = a.AddSamples(ssn, "rain", samples)
		err = a.FN34AddRain(ssn, samples)
	}

	//水位数据
	if len(data.WaterSample) > 0 {
		xlog.Info("FN34 water")
		//先处理timestamp，到整点整5分
		n := len(data.WaterSample)
		start := endTime5minute(data.WaterSampleTimestamp, n)

		var samples []*Sl651Data
		for i, one := range data.WaterSample {
			samples = append(samples, &Sl651Data{
				ValueNumber: one,
				Timestamp:   start + int64(i*300),
			})
		}
		err = a.AddSamples(ssn, "water", samples)
	}

	//电压数据采集
	var samplesVoltage []*Sl651Data
	samplesVoltage = append(samplesVoltage, &Sl651Data{
		Timestamp:   data.VolageSampleTimestamp,
		ValueNumber: data.VolageSample,
	})

	err = a.AddSamples(ssn, "battery", samplesVoltage)

	return
}

// FN37  中心站查询遥测站实时数据
func (a *Adapter) handleN36(ssn string, data *bean.ResponseFn36) (err error) {
	var arr []*Sl651Data
	arr = append(arr, &Sl651Data{
		Timestamp:   data.Timestamp,
		ValueString: data.ImageUrl,
	})
	return a.AddSamples(ssn, "image", arr)
}
