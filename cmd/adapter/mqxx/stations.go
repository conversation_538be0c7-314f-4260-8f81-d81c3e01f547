package mqxx

import (
	"bufio"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"bs.com/ebox/pkg/bean"
	"bs.com/ebox/pkg/httpclient"
	"bs.com/ebox/pkg/xlog"

	"bs.com/ebox/cmd/adapter/config"
	"bs.com/ebox/pkg/xutils"
)

func parseStatonSN() ([]string, error) {
	filename := "station_list.txt"
	//路径
	filename = filepath.Join(xutils.GetExecPath(), filename)
	f, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var arr []string
	// read the file line by line using scanner
	scanner := bufio.NewScanner(f)
	for scanner.Scan() {
		// do something with a line
		line := scanner.Text()
		arr = append(arr, strings.TrimSpace(line))
	}

	if err = scanner.Err(); err != nil {
		return nil, err
	}

	return arr, nil
}

func listStationRealtime() ([]*bean.StationMeta, error) {
	areaCode := config.Get().Misc.AreaCode
	host := config.Get().Misc.HttpHost

	data := map[string]interface{}{
		"area_code": areaCode,
	}

	urlStr := fmt.Sprintf("%s/api/adapter/list_station", host)
	resp, err := httpclient.GET(urlStr, data)
	if err != nil {
		xlog.Error("list station  err: ", err.Error())
		return nil, err
	}
	if resp.Code != 0 {
		xlog.Error("list station  err: ", resp.Message)
		return nil, errors.New("list station failed")
	}

	type respData struct {
		Len  int                 `json:"len"`
		List []*bean.StationMeta `json:"list"`
	}
	var body respData
	err = json.Unmarshal(resp.Payload, &body)
	if err != nil {
		fmt.Println("json parse err:", err)
		return nil, err
	}

	xlog.Info("list station len = ", body.Len)
	return body.List, nil
}
