package mqxx

import (
	"strings"

	"bs.com/ebox/cmd/adapter/model"
	"bs.com/ebox/pkg/bean"
)

// 测站 类型映射
// 处理 report 消息
// report 消息来自水利服务器，带有雨量 水位 电压等传感器信息
func (a *Adapter) StationInfoUpsert(st *bean.StationMeta) error {

	one := &model.ST_STBPRP_B{
		STCD:   st.StationSN,
		STNM:   st.Name,
		LGTD:   st.Lng,
		LTTD:   st.Lat,
		STLC:   st.Address,
		ADDVCD: st.AreaCode,
	}

	//站点类型:雨量站PP,雨量水位站：PR
	sensors := strings.Join(st.Sensors, "-")
	if strings.Index(sensors, "rain") != -1 {
		if strings.Index(sensors, "water") != -1 {
			one.STTP = "PR"
		} else {
			one.STTP = "PP"
		}
	}

	// 更新缓存：测站编码 —— 测站类型，每次都更新
	a.mStationCategory.Store(st.StationSN, st.Category)

	//xlog.Debug("upsert station : ", xutils.JSONString(one))
	//测站是否存在，如果存在则更新，不存在，则添加

	return a.UpsertSTBPRP(one)
}
