## adapter sql server

- 程序运行在客户的服务器上。
- 通过 mqtt 订阅数据。
- sl651服务这边需要根据客户提供的测站列表，把一些测站的数据，通过 mqtt 的方式，发布出去。
- 按照客户提供的数据库访问接口，把数据存储起来。



## mssql

```
ST_STBPRP_B
ST_PPTN_R
ST_RSVR_R
ST_RIVER_R
ST_IMGINFO_R
ST_VOLTAGE_R
ST_FLOW_R
```

```sql
查看所有数据库
SELECT Name from sys.Databases;


use xxx;
查看所有表
select name from sysobjects;
Select Name FROM SysObjects Where XType='U' orDER BY Name

创建库
```
create database adapter;
```


库名：
select name from master.dbo.sysdatabases;
表名：
select name  from adapter.dbo.sysobjects where xtype = 'u' and name  not in('emails','uagents');

列名：
select name from syscolumns where id=(select id from sysobjects where  name = 'users') and name<>'id'

数据：
select top 1 username+':'+ password from test.dbo.users
```

表结构
```
方式一：显示多种信息
sp_help table_name

方式二：只显示字段
sp_columns table_name
```

## 分页

在 Microsoft SQL Server (MSSQL) 中，可以使用 OFFSET 和 FETCH 子句来获取分页数据。
具体来说，OFFSET 子句用于指定从哪一行开始返回数据，FETCH 子句用于指定返回的行数。例如，要获取第 11 到 20 行的数据，可以按照以下方式编写查询语句：
sqlCopy code
SELECT *
FROM your_table
ORDER BY some_column
OFFSET 10 ROWS
FETCH NEXT 10 ROWS ONLY;
在这个查询语句中，OFFSET 10 ROWS 表示从第 11 行开始返回数据，FETCH NEXT 10 ROWS ONLY 表示返回 10 行数据。
需要注意的是，在使用 OFFSET 和 FETCH 子句时，必须先使用 ORDER BY 子句对数据进行排序，否则返回的数据顺序是不确定的。
另外，如果你使用的是 MSSQL 2012 及更早版本，OFFSET 和 FETCH 子句可能不被支持。此时可以使用 ROW_NUMBER() 函数结合子查询来实现分页查询，具体可以参考以下示例：
sqlCopy code
SELECT *
FROM (
    SELECT *, ROW_NUMBER() OVER (ORDER BY some_column) AS row_num
    FROM your_table
) AS subquery
WHERE subquery.row_num BETWEEN 11 AND 20;
在这个查询语句中，ROW_NUMBER() 函数用于为每一行数据分配一个行号，ORDER BY 子句用于指定排序方式，BETWEEN 11 AND 20 表示返回第 11 到 20 行的数据。
