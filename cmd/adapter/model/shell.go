package model

import (
	"errors"
	"fmt"

	"bs.com/ebox/pkg/xlog"
)

// 读取某个表最新的 10 条数据
func ReadTable(tbl string) (any, error) {
	xlog.Debug("read table : ", tbl)

	var err error
	switch tbl {
	case "ST_STBPRP_B":
		var arr []*ST_STBPRP_B
		err = DB.Raw(fmt.Sprintf("select top 10 * from %s", "ST_STBPRP_B")).Scan(&arr).Error
		return arr, err
	case "ST_PPTN_R":
		var arr []*ST_PPTN_R
		err = DB.Raw(fmt.Sprintf("select top 10 * from %s order by TM DESC", "ST_PPTN_R")).Scan(&arr).Error
		return arr, err
	case "ST_RSVR_R":
		var arr []*ST_RSVR_R
		err = DB.Raw(fmt.Sprintf("select top 10 * from %s order by TM DESC", "ST_RSVR_R")).Scan(&arr).Error
		return arr, err
	case "ST_RIVER_R":
		var arr []*ST_RIVER_R
		err = DB.Raw(fmt.Sprintf("select top 10 * from %s order by TM DESC", "ST_RIVER_R")).Scan(&arr).Error
		return arr, err
	case "ST_IMGINFO_R":
		var arr []*ST_IMGINFO_R
		err = DB.Raw(fmt.Sprintf("select top 10 * from %s order by TM DESC", "ST_IMGINFO_R")).Scan(&arr).Error
		return arr, err
	case "ST_VOLTAGE_R":
		var arr []*ST_VOLTAGE_R
		err = DB.Raw(fmt.Sprintf("select top 10 * from %s order by TM DESC", "ST_VOLTAGE_R")).Scan(&arr).Error
		return arr, err
	case "ST_FLOW_R":
		var arr []*ST_FLOW_R
		err = DB.Raw(fmt.Sprintf("select top 10 * from %s order by TM DESC", "ST_FLOW_R")).Scan(&arr).Error
		return arr, err
	default:
		err = errors.New("invalid table name:" + tbl)
		return nil, err
	}
}
