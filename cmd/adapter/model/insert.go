package model

import (
	"bs.com/ebox/pkg/bean"
	"bs.com/ebox/pkg/xlog"
	"gorm.io/gorm"
)

type Dao struct {
	db *gorm.DB
}

func NewDao() *Dao {
	return &Dao{db: DB}
}

// 添加或者更新测站基本信息
func (d *Dao) UpsertSTBPRP(info *ST_STBPRP_B) error {
	info.STCD = info.STCD[2:] //截取后面的 8 位
	var one ST_STBPRP_B
	err := d.db.Model(&ST_STBPRP_B{}).Where("STCD = ?", info.STCD).First(&one).Error
	if err != nil {
		xlog.Info("add new ST_STBPRP_B")
		return d.db.Create(info).Error
	} else {
		xlog.Info("update ST_STBPRP_B")
		m := bean.StructToMap(info)
		return d.db.Model(&ST_STBPRP_B{}).Where("STCD = ?", info.STCD).Updates(m).Error
	}
}

// 测站是否已经存在
func (d *Dao) HasSTBPRP(stcode string) bool {
	stcode = stcode[2:] //截取后面的 8 位
	var one ST_STBPRP_B
	err := d.db.Model(&ST_STBPRP_B{}).Where("STCD = ?", stcode).First(&one).Error
	return err == nil
}

// 二、雨量实时表
func (d *Dao) AddPPTN(one *ST_PPTN_R) error {
	return d.db.Create(one).Error
}

// 三、水库水位实时表
func (d *Dao) AddRSVR(one *ST_RSVR_R) error {
	return d.db.Create(one).Error
}

// 四、河道水位实时表
func (d *Dao) AddRIVER(one *ST_RIVER_R) error {
	return d.db.Create(one).Error
}

// 五、图片数据实时表
func (d *Dao) AddIMGINFO(one *ST_IMGINFO_R) error {
	return d.db.Create(one).Error
}

// 六、站点电压实时表
func (d *Dao) AddVOLTAGE(one *ST_VOLTAGE_R) error {
	return d.db.Create(one).Error
}

// 七、流量实时表
func (d *Dao) AddFLOW(one *ST_FLOW_R) error {
	return d.db.Create(one).Error
}
