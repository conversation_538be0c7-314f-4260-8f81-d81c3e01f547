package model

import (
	"time"
)

// 一、站点基本信息表
// 站点类型:雨量站PP,雨量水库站：PR，水库站：RR, 雨量河道站PZ，河道站：ZZ，流量站：FF
type ST_STBPRP_B struct {
	STCD           string `gorm:"column:STCD;type:varchar(8);primarykey"     json:"STCD"`           // 站点编码，主键
	STNM           string `gorm:"column:STNM;type:varchar(30);not null;"     json:"STNM"`           // 站点名称，非空字段
	LGTD           string `gorm:"column:LGTD;type:varchar(20);not null;"     json:"LGTD"`           // 经度，非空字段
	LTTD           string `gorm:"column:LTTD;type:varchar(20);not null;"     json:"LTTD"`           // 纬度，非空字段
	STTP           string `gorm:"column:STTP;type:varchar(3)"                json:"STTP"`           // 站点类型:雨量站PP,雨量水位站：PR
	STLC           string `gorm:"column:STLC;type:varchar(50)"               json:"STLC"`           // 站点位置，可空
	ADDVCD         string `gorm:"column:ADDVCD;type:varchar(10)"             json:"ADDVCD"`         // 行政区编码,可空
	WSCD           string `gorm:"column:WSCD;type:varchar(20)"               json:"WSCD"`           // 水库编码，可空
	RVNM           string `gorm:"column:RVNM;type:varchar(30)"               json:"RVNM"`           // 站点所在河流，可空
	VdeiocameraUrl string `gorm:"column:VdeiocameraUrl;type:varchar(256)"    json:"VdeiocameraUrl"` // 视频站http访问地址，可空
}

// 二、雨量实时表
type ST_PPTN_R struct {
	STCD string    `gorm:"column:STCD;type:varchar(8);index;unique;"   json:"STCD"` // 站点编码，主键
	TM   time.Time `gorm:"column:TM;type:datetime;index;unique;"       json:"TM"`   // 来报时间，主键
	DRP  float64   `gorm:"column:DRP;type:decimal(5, 1)"            json:"DRP"`     // 雨量
	INTV float64   `gorm:"column:INTV;type:decimal(5, 2)"           json:"INTV"`    // 雨量间隔，0.05表示5分钟，1 就是 1小时雨量
	FLAG int       `gorm:"column:FLAG;type:smallint"                json:"FLAG"`    // 默认为0

}

// 三、水库水位实时表
type ST_RSVR_R struct {
	STCD string    `gorm:"column:STCD;type:varchar(8);index;unique;"   json:"STCD"` // 站点编码，主键
	TM   time.Time `gorm:"column:TM;type:datetime;index;unique;"       json:"TM"`   // 来报时间，主键
	RZ   float64   `gorm:"column:RZ;type:decimal(7, 3)"              json:"RZ"`     // 水位，非空字段
	W    float64   `gorm:"column:W;type:decimal(9, 3)"               json:"W"`      // 库容，可空字段
	FLAG int       `gorm:"column:FLAG;type:smallint"                 json:"FLAG"`   // 默认为0

}

// 四、河道水位实时表
type ST_RIVER_R struct {
	STCD string    `gorm:"column:STCD;type:varchar(8);index;unique;"   json:"STCD"` // 站点编码，主键
	TM   time.Time `gorm:"column:TM;type:datetime;index;unique;"       json:"TM"`   // 来报时间，主键
	Z    float64   `gorm:"column:Z;type:decimal(7, 3)"       json:"Z"`              // 水位，非空字段
	Q    float64   `gorm:"column:Q;type:decimal(9, 3)"       json:"Q"`              // 库容，可空字段
	FLAG int       `gorm:"column:FLAG;type:smallint"         json:"FLAG"`           // 默认为0
}

// 五、图片数据实时表
type ST_IMGINFO_R struct {
	STCD   string    `gorm:"column:STCD;type:varchar(8);index;unique;"   json:"STCD"` // 站点编码，主键
	TM     time.Time `gorm:"column:TM;type:datetime;index;unique;"       json:"TM"`   // 来报时间，主键
	VTDT   string    `gorm:"column:VTDT;type:varchar(100)"       json:"VTDT"`         // 图片绝对路径,或者网络路径，
	BLANCE int       `gorm:"column:BLANCE;type:smallint"         json:"BLANCE"`       // 当一个站点有多个摄像头时，此处标注为第几号摄像头图的编号（1,2,3,4）
	FLAG   int       `gorm:"column:FLAG;type:smallint"           json:"FLAG"`         // 默认为0
}

// 六、站点电压实时表
type ST_VOLTAGE_R struct {
	STCD string    `gorm:"column:STCD;type:varchar(8);index;unique;"   json:"STCD"` // 站点编码，主键
	TM   time.Time `gorm:"column:TM;type:datetime;index;unique;"       json:"TM"`   // 来报时间，主键
	V    float64   `gorm:"column:V;type:decimal(5, 2)"        json:"V"`             // 电压值
	FLAG int       `gorm:"column:FLAG;type:smallint"          json:"FLAG"`          // 默认为0
}

// 七、流量实时表
type ST_FLOW_R struct {
	STCD string    `gorm:"column:STCD;type:varchar(8);index;unique;"   json:"STCD"` // 站点编码，主键
	TM   time.Time `gorm:"column:TM;type:datetime;index;unique;"       json:"TM"`   // 来报时间，主键
	CF   float64   `gorm:"column:CF;type:decimal(8, 3)"      json:"CF"`             // 瞬时流量
	SF   float64   `gorm:"column:SF;type:decimal(12, 3)"     json:"SF"`             // 总流量
	F1   float64   `gorm:"column:F1;type:decimal(12, 3)"     json:"F1"`             // 备用1
	F2   float64   `gorm:"column:F2;type:decimal(12, 3)"     json:"F2"`             // 备用2
	FLAG int       `gorm:"column:FLAG;type:smallint"         json:"FLAG"`           // 默认为0
}
