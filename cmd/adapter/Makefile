# Go parameters
BINARY_NAME=adapter
VERSION="v0.1.0"
DATE= `date +%Y%m%d%H%M%S`

uname=`uname -s`
goos=$(shell echo $(uname) | tr A-Z a-z)

.PHONY: pc clean run

pc:
		@echo version: ${VERSION}
		@echo date: ${DATE}
		@echo os: ${goos}
		CGO_ENABLED=0 GOOS=${goos} GOARCH=amd64 go build -ldflags "-X main.BuildTime=$(date -u +%Y%m%d.%H%M%S)" -o ${BINARY_NAME}_${goos}.bin


win:
		CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -ldflags "-X main.BuildTime=$(date -u +%Y%m%d.%H%M%S)" -o ${BINARY_NAME}.exe
clean:
		go clean
		@if [ -f ${BINARY_NAME}_linux ] ; then rm ${BINARY_NAME}_linux ; fi
		@if [ -f ${BINARY_NAME}_mac ] ; then rm ${BINARY_NAME}_mac ; fi

run:
		./$(BINARY_NAME)

