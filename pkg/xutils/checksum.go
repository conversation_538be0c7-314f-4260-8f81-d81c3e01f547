package xutils

import (
	"bufio"
	"crypto/md5"
	"encoding/hex"
	"io"
	"os"

	"bs.com/ebox/pkg/xlog"
)

// FileMd5 : generate md5 for a file
func FileMd5(filename string) (string, error) {

	f, err := os.Open(filename)
	if err != nil {
		xlog.Info("open file failed:", err.Error())
		return "", err
	}
	defer f.Close()

	md5hash := md5.New()
	if _, err := io.Copy(md5hash, f); err != nil {
		xlog.Info("copy file failed: ", err.Error())
		return "", err
	}

	buf := md5hash.Sum(nil)
	hexStr := hex.EncodeToString(buf[:])

	return hexStr, nil
}

// file crc16
func FileCRC16(filename string) (uint16, error) {
	f, err := os.Open(filename)
	if err != nil {
		xlog.Info("crc16 open file failed : ", err.<PERSON>rror())
		return 0, err
	}
	defer f.Close()

	crcer := NewCrc16Writer()
	_, err = io.Copy(crcer, f)
	if err != nil {
		xlog.Info("io copy failed : ", err.Error())

		return 0, err
	}
	return crcer.Result(), nil
}

// 文件和校验
func FileCheckSum32(filename string) (uint32, error) {
	if info, err := os.Stat(filename); err != nil || info.IsDir() {
		return 0, err
	}

	f, err := os.Open(filename)
	if err != nil {
		return 0, err
	}
	defer f.Close()

	var result uint32 = 0

	buf := make([]byte, 4094) // 4k
	reader := bufio.NewReader(f)
	for {
		n, err := reader.Read(buf)
		if err == nil {
			for _, one := range buf[:n] {
				result += uint32(one)
			}
		} else if err == io.EOF {
			//读完了
			break
		} else {
			//错误
			return 0, err
		}
	}

	return result, nil
}
