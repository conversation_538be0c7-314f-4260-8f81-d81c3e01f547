package xbin

import (
	"encoding/binary"
	"math"

	"bs.com/ebox/pkg/xutils"
)

type PacketEncoder interface {
	Dump() string

	PutBool(in bool)

	PutInt8(in int8)
	PutInt16(in int16)
	PutInt32(in int32)
	PutInt64(in int64)

	PutUint8(in uint8)
	PutUint16(in uint16)
	PutUint32(in uint32)
	PutUint64(in uint64)

	PutFloat32(in float32)
	PutFloat64(in float64)

	PutArrayLength(in int) error
	PutRawBytes(in []byte) error
	PutBytes(in []byte) error
	PutRawString(in string) error
	PutString(in string) error
	PutNullableString(in *string) error
	PutStringArray(in []string) error

	PutInt16Array(in []int16) error
	PutInt32Array(in []int32) error
	PutInt64Array(in []int64) error

	PutUint16Array(in []uint16) error
	PutUint32Array(in []uint32) error
	PutUint64Array(in []uint64) error

	Push(pe PushEncoder)
	Pop()
}

// PushEncoder is the interface for encoding fields like CRCs and lengths where the value
// of the field depends on what is encoded after it in the packet. Start them with PacketEncoder.Push() where
// the actual value is located in the packet, then PacketEncoder.Pop() them when all the bytes they
// depend upon have been written.
type PushEncoder interface {
	// Saves the offset into the input buffer as the location to actually write the calculated value when able.
	SaveOffset(in int)

	// Returns the length of data to reserve for the output of this encoder (eg 4 bytes for a CRC32).
	// ReserveSize() int
	PushReserveSize() int // push的时候，保存数据到当前位置，需要占用的空间。比如 size_field 仅入栈的时候预留空间
	PopReserveSize() int  // pop 的时候，保存数据到当前位置，需要占用的空间。比如 crc_field 仅出栈的时候预留空间

	// Indicates that all required data is now available to calculate and write the field.
	// SaveOffset is guaranteed to have been called first. The implementation should write ReserveLength() bytes
	// of data to the saved offset, based on the data between the saved offset and curOffset.
	Fill(curOffset int, buf []byte) error
}

type Encoder interface {
	Encode(e PacketEncoder) error
}

// 不使用 length encode，预分配1500字节的 buffer
// sync.Pool 使用缓存池优化
func Encode(e Encoder, byteOrder binary.ByteOrder) ([]byte, error) {
	b := make([]byte, 1500)
	byteEnc := NewByteEncoder(b, byteOrder)
	err := e.Encode(byteEnc)
	if err != nil {
		return nil, err
	}
	return b[:byteEnc.off], nil
}

type ByteEncoder struct {
	b         []byte
	off       int
	stack     []PushEncoder
	byteOrder binary.ByteOrder
}

func (b *ByteEncoder) Dump() string {
	return xutils.DumpHexRaw(b.b[:b.off])
}

func (b *ByteEncoder) Bytes() []byte {
	return b.b[:b.off]
}

func NewByteEncoder(b []byte, byteOrder binary.ByteOrder) *ByteEncoder {
	return &ByteEncoder{
		b:         b,
		byteOrder: byteOrder,
	}
}

func (e *ByteEncoder) PutBool(in bool) {
	if in {
		e.b[e.off] = byte(int8(1))
	}
	e.off++
}

func (e *ByteEncoder) PutInt8(in int8) {
	e.b[e.off] = byte(in)
	e.off++
}

func (e *ByteEncoder) PutInt16(in int16) {
	e.byteOrder.PutUint16(e.b[e.off:], uint16(in))
	e.off += 2
}

func (e *ByteEncoder) PutInt32(in int32) {
	e.byteOrder.PutUint32(e.b[e.off:], uint32(in))
	e.off += 4
}

func (e *ByteEncoder) PutInt64(in int64) {
	e.byteOrder.PutUint64(e.b[e.off:], uint64(in))
	e.off += 8
}

func (e *ByteEncoder) PutUint8(in uint8) {
	e.b[e.off] = byte(in)
	e.off++
}

func (e *ByteEncoder) PutUint16(in uint16) {
	e.byteOrder.PutUint16(e.b[e.off:], in)
	e.off += 2
}

func (e *ByteEncoder) PutUint32(in uint32) {
	e.byteOrder.PutUint32(e.b[e.off:], in)
	e.off += 4
}

func (e *ByteEncoder) PutUint64(in uint64) {
	e.byteOrder.PutUint64(e.b[e.off:], in)
	e.off += 8
}

func (e *ByteEncoder) PutFloat32(in float32) {
	e.byteOrder.PutUint32(e.b[e.off:], math.Float32bits(in))
	e.off += 4
}

func (e *ByteEncoder) PutFloat64(in float64) {
	e.byteOrder.PutUint64(e.b[e.off:], math.Float64bits(in))
	e.off += 8
}

// 数组元素个数
func (e *ByteEncoder) PutArrayLength(in int) error {
	e.PutUint16(uint16(in))
	return nil
}

// 不带数组长度
func (e *ByteEncoder) PutRawBytes(in []byte) error {
	copy(e.b[e.off:], in)
	e.off += len(in)
	return nil
}

// 带数组长度
func (e *ByteEncoder) PutBytes(in []byte) error {
	if in == nil {
		e.PutUint16(0)
		return nil
	}
	e.PutUint16(uint16(len(in)))
	return e.PutRawBytes(in)
}

// 不带字符串长度
func (e *ByteEncoder) PutRawString(in string) error {
	copy(e.b[e.off:], in)
	e.off += len(in)
	return nil
}

func (e *ByteEncoder) PutString(in string) error {
	e.PutUint16(uint16(len(in)))
	copy(e.b[e.off:], in)
	e.off += len(in)
	return nil
}

func (e *ByteEncoder) PutNullableString(in *string) error {
	if in == nil {
		// 长度为0
		e.PutUint16(0)
		return nil
	}
	return e.PutString(*in)
}

func (e *ByteEncoder) PutStringArray(in []string) error {
	err := e.PutArrayLength(len(in))
	if err != nil {
		return err
	}

	for _, val := range in {
		if err := e.PutString(val); err != nil {
			return err
		}
	}

	return nil
}

func (e *ByteEncoder) PutInt16Array(in []int16) error {
	err := e.PutArrayLength(len(in))
	if err != nil {
		return err
	}
	for _, val := range in {
		e.PutInt16(val)
	}
	return nil
}

func (e *ByteEncoder) PutInt32Array(in []int32) error {
	err := e.PutArrayLength(len(in))
	if err != nil {
		return err
	}
	for _, val := range in {
		e.PutInt32(val)
	}
	return nil
}

func (e *ByteEncoder) PutInt64Array(in []int64) error {
	err := e.PutArrayLength(len(in))
	if err != nil {
		return err
	}
	for _, val := range in {
		e.PutInt64(val)
	}
	return nil
}

func (e *ByteEncoder) PutUint16Array(in []uint16) error {
	err := e.PutArrayLength(len(in))
	if err != nil {
		return err
	}
	for _, val := range in {
		e.PutUint16(val)
	}
	return nil
}

func (e *ByteEncoder) PutUint32Array(in []uint32) error {
	err := e.PutArrayLength(len(in))
	if err != nil {
		return err
	}
	for _, val := range in {
		e.PutUint32(val)
	}
	return nil
}

func (e *ByteEncoder) PutUint64Array(in []uint64) error {
	err := e.PutArrayLength(len(in))
	if err != nil {
		return err
	}
	for _, val := range in {
		e.PutUint64(val)
	}
	return nil
}

func (e *ByteEncoder) Push(pe PushEncoder) {
	pe.SaveOffset(e.off)

	reserved := pe.PushReserveSize()
	if reserved > 0 {
		e.off += reserved
	}

	e.stack = append(e.stack, pe)
}

func (e *ByteEncoder) Pop() {
	// this is go's ugly pop pattern (the inverse of append)
	pe := e.stack[len(e.stack)-1]
	e.stack = e.stack[:len(e.stack)-1]

	_ = pe.Fill(e.off, e.b)

	reserved := pe.PopReserveSize()
	if reserved > 0 {
		e.off += reserved
	}

}
