package oss

import (
	"fmt"
	"io"
	"os"
	"time"

	"bs.com/ebox/config"
	"bs.com/ebox/pkg/now"
	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xutils"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

// upload to OSS
// 设备上传的图像，保存到 oss
func Upload(ssn, path string) (string, error) {
	bucketName := config.Get().Aliyun.OssBucketImage
	var client, err = NewOSS(bucketName)
	if err != nil {
		return "", err
	}

	//dateStr yyyy-mm-dd
	//timeStr hh-mm-ss
	dateStr, timeStr := now.TimeUTCtoLocal4(time.Now().Unix())
	fileType := xutils.FileType(path)

	//ssn/yyyy-mm-dd/hh-mm-ss.jpg
	objName := fmt.Sprintf("%s/%s/%s.%s", ssn, dateStr, timeStr, fileType)

	return client.UpLoad(objName, path)
}

// ==========================================================================================
/*
// yourEndpoint填写Bucket对应的Endpoint，以华东1（杭州）为例，填写为 https://oss-cn-hangzhou.aliyuncs.com。其它Region请按实际情况填写。
endpoint := "yourEndpoint"
// 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
accessKeyId := "yourAccessKeyId"
accessKeySecret := "yourAccessKeySecret"

AccessKey ID
LTAI5tG16TcQFZMsBxt2uqgJ

AccessKey Secret
******************************
*/
const (
	Endpoint        = "oss-cn-shenzhen.aliyuncs.com"   // Endpoint 访问域名
	AccessKeyID     = "LTAI5tG16TcQFZMsBxt2uqgJ"       // AccessKeyID AK
	AccessKeySecret = "******************************" // AccessKeySecret AKS
)

type ALiYunOSS struct {
	Client *oss.Client

	Endpoint        string // Endpoint 访问域名
	AccessKeyID     string // AccessKeyID AK
	AccessKeySecret string // AccessKeySecret AKS
	BucketName      string // BucketName 桶名称
}

/*
// yourBucketName填写Bucket名称。
bucketName := "yourBucketName"
*/
func NewOSS(bucketName string) (e *ALiYunOSS, err error) {
	e = &ALiYunOSS{
		BucketName:      bucketName,
		Endpoint:        Endpoint,
		AccessKeyID:     AccessKeyID,
		AccessKeySecret: AccessKeySecret,
	}

	var client *oss.Client
	client, err = oss.New(e.Endpoint, e.AccessKeyID, e.AccessKeySecret)
	if err != nil {
		return nil, err
	}

	e.Client = client

	//检查bucket是否存在，不存在则创建
	exist, err := e.Client.IsBucketExist(bucketName)
	if !exist {
		err = e.Client.CreateBucket(bucketName, oss.ACL(oss.ACLPublicRead))
	}

	return
}

// UpLoad 文件上传
/*
// yourObjectName填写Object完整路径，完整路径不包含Bucket名称。
objectName := "yourObjectName"
// yourLocalFileName填写本地文件的完整路径。
localFileName := "yourLocalFileName"
*/
func (e *ALiYunOSS) UpLoad(objName, localFile string) (url string, err error) {
	// 获取存储空间。
	var bucket *oss.Bucket
	bucket, err = e.Client.Bucket(e.BucketName)
	if err != nil {
		xlog.Error("upload failed", "err", err)
		return
	}

	// 设置分片大小为100 KB，指定分片上传并发数为3，并开启断点续传上传。
	// 其中<yourObjectName>与objectKey是同一概念，表示断点续传上传文件到OSS时需要指定包含文件后缀在内的完整路径，例如abc/efg/123.jpg。
	// "LocalFile"为filePath，100*1024为partSize。
	//err = bucket.PutObjectFromFile(yourObjectName, localFile)
	err = bucket.UploadFile(objName, localFile, 100*1024, oss.Routines(3), oss.Checkpoint(true, ""))
	if err != nil {
		xlog.Error("upload failed", "err", err)
		return
	}

	//https: //test-ebox-123.oss-cn-shenzhen.aliyuncs.com/test.jpg
	//https://test-ebox-123.oss-cn-shenzhen.aliyuncs.com/2022-12-03/115327.jpg
	url = fmt.Sprintf("https://%s.%s/%s", e.BucketName, e.Endpoint, objName)
	return
}

func (e *ALiYunOSS) UpLoadImage(localFile string) (url string, err error) {
	//dateStr yyyy-mm-dd
	//timeStr hh-mm-ss
	dateStr, timeStr := now.TimeUTCtoLocal4(time.Now().Unix())
	fileType := xutils.FileType(localFile)

	//yyyy-mm-dd/hh-mm-ss.jpg
	objName := fmt.Sprintf("%s/%s.%s", dateStr, timeStr, fileType)

	return e.UpLoad(objName, localFile)
}

// UpLoad 文件上传
func (e *ALiYunOSS) DownLoad(yourObjectName, localFile string) error {
	// 获取存储空间。
	bucket, err := e.Client.Bucket(e.BucketName)
	if err != nil {
		xlog.Error("download failed", "err", err)
		return err
	}

	// 依次填写Object的完整路径（例如exampledir/exampleobject.txt）和本地文件的完整路径
	//err = bucket.GetObjectToFile(yourObjectName, localFile)

	// 设置分片大小为100 KB，指定分片上传并发数为3，并开启断点续传上传。
	// 其中<yourObjectName>与objectKey是同一概念，表示断点续传上传文件到OSS时需要指定包含文件后缀在内的完整路径，例如abc/efg/123.jpg。
	// "LocalFile"为filePath，100*1024为partSize。
	err = bucket.DownloadFile(yourObjectName, localFile, 100*1024, oss.Routines(3), oss.Checkpoint(true, ""))
	if err != nil {
		xlog.Error("download failed", "err", err)
		return err
	}
	return nil
}

// 指定范围下载文件
// 调用方必须保证范围合法且正确
// 假设现有大小为1000 Bytes的Object，则指定的正常下载范围应为0~999。如果指定范围不在有效区间，会导致Range不生效，响应返回值为200，并传送整个Object的内容。
func (e *ALiYunOSS) DownloadRange(yourObjectName string, start, end int64) ([]byte, error) {
	// 获取存储空间。
	bucket, err := e.Client.Bucket(e.BucketName)
	if err != nil {
		xlog.Error("download failed", "err", err)
		return nil, err
	}

	// oss.Range(15, 35) 获取15~35字节范围内的数据，包含15和35，共21个字节的数据。
	// 如果指定的范围无效（比如开始或结束位置的指定值为负数，或指定值大于文件大小），则下载整个文件。
	// yourObjectName填写不包含Bucket名称在内的Object的完整路径。
	body, err := bucket.GetObject(yourObjectName, oss.Range(start, end))
	if err != nil {
		return nil, err
	}
	// 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
	defer body.Close()

	data, err := io.ReadAll(body)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (e *ALiYunOSS) Delete(yourObjectName string) error {
	// 获取存储空间。
	bucket, err := e.Client.Bucket(e.BucketName)
	if err != nil {
		xlog.Error("delete failed", "err", err)
		return err
	}

	// 其中<yourObjectName>与objectKey是同一概念，表示上传文件到OSS时需要指定包含文件后缀在内的完整路径，例如abc/efg/123.jpg。
	err = bucket.DeleteObject(yourObjectName)
	if err != nil {
		xlog.Error("delete failed", "err", err)
		return err
	}
	return nil
}

func (e *ALiYunOSS) ListAll() ([]string, error) {
	// 获取存储空间。
	bucket, err := e.Client.Bucket(e.BucketName)
	if err != nil {
		xlog.Error("list all failed", "err", err)
		return nil, err
	}

	// 列举文件
	var arr []string
	marker := ""
	for {
		lsRes, err := bucket.ListObjects(oss.Marker(marker))
		if err != nil {
			return nil, err
		}
		// 打印列举文件，默认情况下一次返回100条记录。
		for _, object := range lsRes.Objects {
			fmt.Println("Bucket: ", object.Key)
			arr = append(arr, object.Key)
		}
		if lsRes.IsTruncated {
			marker = lsRes.NextMarker
		} else {
			break
		}
	}

	return arr, nil
}

// 获取存储空间的信息，包括地域（Region或Location）、创建日期（CreationDate）、访问权限（ACL）、拥有者（Owner）、存储类型（StorageClass）、容灾类型（RedundancyType）等。
func (e *ALiYunOSS) Info() {
	// yourBucketName填写存储空间名称。
	res, err := e.Client.GetBucketInfo(e.BucketName)
	if err != nil {
		fmt.Println("Error:", err)
		os.Exit(-1)
	}
	fmt.Println("BucketInfo.Location: ", res.BucketInfo.Location)
	fmt.Println("BucketInfo.CreationDate: ", res.BucketInfo.CreationDate)
	fmt.Println("BucketInfo.ACL: ", res.BucketInfo.ACL)
	fmt.Println("BucketInfo.Owner: ", res.BucketInfo.Owner)
	fmt.Println("BucketInfo.StorageClass: ", res.BucketInfo.StorageClass)
	fmt.Println("BucketInfo.RedundancyType: ", res.BucketInfo.RedundancyType)
	fmt.Println("BucketInfo.ExtranetEndpoint: ", res.BucketInfo.ExtranetEndpoint)
	fmt.Println("BucketInfo.IntranetEndpoint: ", res.BucketInfo.IntranetEndpoint)
}
