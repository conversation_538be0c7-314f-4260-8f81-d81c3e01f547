package bmap

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"bs.com/ebox/pkg/xlog"
)

// 默认 ak
const bMapAK = "********************************"

// 通过 GPS 坐标反查出来的地址

type geoAddress struct {
	Country  string `json:"country"       bson:"country"`  // 国家
	Province string `json:"province"      bson:"province"` // 省
	City     string `json:"city"          bson:"city"`     // 地级市
	District string `json:"district"      bson:"district"` // 区、县或者县级市
	Town     string `json:"town"          bson:"town"`     // 乡镇
	Adcode   string `json:"adcode"   bson:"adcode"`        // 行政区划码
	Towncode string `json:"towncode" bson:"towncode"`      // 乡镇编码，仅高德地图有此项
}

// 定位相关
type GPS struct {
	Lng string `json:"lng" bson:"lng"` // 经度
	Lat string `json:"lat" bson:"lat"` // 维度
}

/*
测试百度地图 API 的逆地理编码 ：
文档地址：http://lbsyun.baidu.com/index.php?title=webapi/guide/webservice-geocoding-abroad
http://api.map.baidu.com/geocoder/v2/?callback=renderReverse&location=35.658651,139.745415&output=json&pois=1&ak=您的ak //GET请求
*/

// 设备和管理员都有地理位置的信息
// 通过 GPS可以拿到 Country Province City District Town

func (a geoAddress) GetChannel() string {
	var arr = make([]string, 0, 10)
	if a.Province != "" {
		arr = append(arr, GetPinYin(a.Province))
	}

	if a.City != "" {
		arr = append(arr, GetPinYin(a.City))
	}

	if a.District != "" {
		arr = append(arr, GetPinYin(a.District))
	}

	if a.Town != "" {
		arr = append(arr, GetPinYin(a.Town))
	}

	return strings.Join(arr, ".")
}

// 拼接中文地址
func (a geoAddress) GetAddress() string {
	var arr = make([]string, 0, 10)
	// if a.Province != "" {
	// 	arr = append(arr, a.Province)
	// }

	if a.City != "" {
		arr = append(arr, a.City)
	}

	if a.District != "" {
		arr = append(arr, a.District)
	}

	if a.Town != "" {
		arr = append(arr, a.Town)
	}

	return strings.Join(arr, "")
}

// 定位相关
type BGPS struct {
	Lng float32 `json:"lng" bson:"lng"` // 经度
	Lat float32 `json:"lat" bson:"lat"` // 维度
}

type GPSToAddrResponse struct {
	Status int `json:"status"`
	Result struct {
		FormattedAddress string `json:"formatted_address"`
		Location         BGPS   `json:"location"`
		AddressComponent struct {
			Country  string `json:"country"       bson:"country"`  // 国家
			Province string `json:"province"      bson:"province"` // 省
			City     string `json:"city"          bson:"city"`     // 地级市
			District string `json:"district"      bson:"district"` // 区、县或者县级市
			Town     string `json:"town"          bson:"town"`     // 乡镇
			Adcode   string `json:"adcode"   bson:"adcode"`        // 行政区划码
		} `json:"addressComponent"`
		CityCode int `json:"cityCode"`
	} `json:"result"`
}

/*
坐标系说明
bd09ll为百度坐标系，在GCJ02坐标系基础上再次加密。其中bd09ll表示百度经纬度坐标，bd09mc表示百度墨卡托米制坐标百度地图默认。
bd09mc（百度米制坐标）
gcj02ll（国测局经纬度坐标，仅限中国）是由中国国家测绘局制订的地理信息系统的坐标系统。由WGS84坐标系经加密后的坐标系。
wgs84ll（ GPS经纬度）为一种大地坐标系，也是目前广泛使用的GPS全球卫星定位系统使用的坐标系。


正/逆地理编码服务默认输入输出坐标类型为百度坐标（BD09），同时可通过参数（"coord_type","ret_coordtype"）控制输入输出坐标类型。
输入坐标支持以上三种坐标系，输出坐标支持-国测局坐标（GCJ02）和百度坐标（BD09）

{
    "status": 0,
    "result": {
        "location": {
            "lng": 111.41309399999997,
            "lat": 26.913510977025794
        },
        "formatted_address": "湖南省邵阳市邵阳县",
        "business": "",
        "addressComponent": {
            "country": "中国",
            "country_code": 0,
            "country_code_iso": "CHN",
            "country_code_iso2": "CN",
            "province": "湖南省",
            "city": "邵阳市",
            "city_level": 2,
            "district": "邵阳县",
            "town": "五峰铺镇",
            "adcode": "430523",
            "street": "",
            "street_number": "",
            "direction": "",
            "distance": ""
        },
        "pois": [],
        "roads": [],
        "poiRegions": [],
        "sematic_description": "鹧鸪村西314米",
        "cityCode": 273
    }
}
*/

// 通过坐标，获取到地址信息
func locationLL2(Lat, Lng string, isBD09 bool) (*geoAddress, *GPS, error) {
	sbUrl := strings.Builder{}
	URL := fmt.Sprintf("http://api.map.baidu.com/geocoder/v2/?ak=%s&location=%s,%s", bMapAK, Lat, Lng)
	sbUrl.WriteString(URL)
	sbUrl.WriteString("&output=json")          // 输出格式为json
	sbUrl.WriteString("&pois=0")               // 是否召回传入坐标周边的poi，0为不召回
	sbUrl.WriteString("&extensions_town=true") // 当取值为true时，行政区划返回乡镇级数据
	sbUrl.WriteString("&extensions_road=true") // 当取值为true时，召回坐标周围最近的3条道路数据。
	sbUrl.WriteString("&ret_coordtype=bd09ll") // 返回百度坐标，顺便做坐标转换
	if isBD09 {
		sbUrl.WriteString("&coordtype=bd09ll") // 坐标的类型，目前支持的坐标类型包括：
	} else {
		sbUrl.WriteString("&coordtype=wgs84ll") // GPS 坐标
	}
	// sbUrl.WriteString("&language_auto=1")
	sbUrl.WriteString("&latest_admin=1")

	urlStr := sbUrl.String()

	Resp, err := http.Get(urlStr)
	if err != nil {
		return nil, nil, err
	}
	defer Resp.Body.Close()

	bodyBuf, err := io.ReadAll(Resp.Body)
	if err != nil {
		return nil, nil, err
	}

	var r = &GPSToAddrResponse{}
	err = json.Unmarshal(bodyBuf, r)
	if err != nil {
		xlog.Error("unmarshal failed", "err", err)
		return nil, nil, err
	}
	if r.Status < 0 {
		return nil, nil, errors.New("somethings wrong")
	}

	ac := r.Result.AddressComponent
	addr := &geoAddress{
		Country:  ac.Country,
		Province: ac.Province,
		City:     ac.City,
		District: ac.District,
		Town:     ac.Town,
		Adcode:   ac.Adcode,
	}
	gps := &GPS{
		Lng: strconv.FormatFloat(float64(r.Result.Location.Lng), 'f', 8, 32),
		Lat: strconv.FormatFloat(float64(r.Result.Location.Lat), 'f', 8, 32),
	}

	return addr, gps, nil
}

func BMapGetGeoAddress(lat, lng string) (*geoAddress, *GPS, error) {
	//TODO: 默认不使用百度坐标系
	return locationLL2(lat, lng, false)
}

type AddrToGPSResponse struct {
	Status int `json:"status"`
	Result struct {
		Location   BGPS   `json:"location"`
		Precise    int    `json:"precise"`
		Confidence int    `json:"confidence"`
		Level      string `json:"level"`
	} `json:"result"`
}

// 地理编码API:从地址，获取到经纬度
func BMapGetLocationByAddr(addr *geoAddress) (*BGPS, error) {
	sbUrl := strings.Builder{}

	URL := fmt.Sprintf("http://api.map.baidu.com/geocoder/v2/?address=%s&ak=%s", addr.GetAddress(), bMapAK)
	sbUrl.WriteString(URL)
	sbUrl.WriteString("&output=json")          // 输出格式为json
	sbUrl.WriteString("&ret_coordtype=bd09ll") // 百度经纬度坐标
	// sbUrl.WriteString("&callback=showLocation") //将json格式的返回值通过callback函数返回以实现jsonp功能

	urlStr := sbUrl.String()

	Resp, err := http.Get(urlStr)
	if err != nil {
		return nil, err
	}
	defer Resp.Body.Close()

	bodyBuf, err := io.ReadAll(Resp.Body)
	if err != nil {
		return nil, err
	}

	var r = &AddrToGPSResponse{}
	err = json.Unmarshal(bodyBuf, r)
	if err != nil {
		xlog.Error("unmarshal failed", "err", err.Error())
		return nil, err
	}
	if r.Status < 0 {
		return nil, errors.New("somethings wrong")
	}
	return &r.Result.Location, nil
}
