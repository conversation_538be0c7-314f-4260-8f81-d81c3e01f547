package bmap

import (
	"errors"
	"strconv"
	"strings"

	"bs.com/ebox/pkg/xlog"
)

//var str1 = "$GPRMC,024813.640,A,3158.4608,N,11848.3737,E,10.05,324.27,150706,,,A*50"
//var str1 = "A,3158.4608,N,11848.3737,E,10.05,324.27,150706,,,A*50"
//var str2 = "A,2240.6400,N,11356.1745,E,0.30,347.09,150918,,,A*70"
/*
数据详解：$GPRMC,<1>,<2>,<3>,<4>,<5>,<6>,<7>,<8>,<9>,<10>,<11>,<12>*hh
　　<1> UTC 时间，hhmmss(时分秒)格式
　　<2> 定位状态，A=有效定位，V=无效定位
　　<3>纬度ddmm.mmmm(度分)格式(前面的0也将被传输)
　　<4> 纬度半球N(北半球)或S(南半球)
　　<5>经度dddmm.mmmm(度分)格式(前面的0也将被传输)
　　<6> 经度半球E(东经)或W(西经)
　　<7>地面速率(000.0~999.9节，前面的0也将被传输)
　　<8>地面航向(000.0~359.9度，以真北为参考基准，前面的0也将被传输)
　　<9> UTC 日期，ddmmyy(日月年)格式
　　<10>磁偏角(000.0~180.0度，前面的0也将被传输)
　　<11> 磁偏角方向，E(东)或W(西)
　　<12>模式指示(仅NMEA01833.00版本输出，A=自主定位，D=差分，E=估算，N=数据无效)
*/

/*
纬度，我们需要把它转换成度分秒的格式，计算方法：如接收到的纬度是：4546.40891
　　4546.40891/100=45.4640891可以直接读出45度, 4546.40891–45*100=46.40891, 可以直接读出46分
　　46.40891–46 =0.40891*60=24.5346读出24秒, 所以纬度是：45度46分24秒。
*/

func parseGprmcStr(str string) (string, error) {
	origin, err := strconv.ParseFloat(str, 32)
	if err != nil {
		return "", err
	}
	var du, fen, miao int

	du = int(origin) / 100
	fen = int(origin) % 100
	miao = int(60 * (origin - float64(du*100+fen)))

	val := float32(du) + float32(fen)/60 + float32(miao)/(60*60)
	return strconv.FormatFloat(float64(val), 'f', 6, 32), nil
}

func ParseGPRMC(str string) (string, string, error) {
	arr := strings.Split(str, ",")
	if arr[0] != "A" {
		xlog.Error("position is invalid")
		return "", "", errors.New("invalid gprmc string")
	}

	latStr := arr[1]
	//latDir := arr[2] // N
	lngStr := arr[3]
	//lngDir := arr[4] // E

	latitude, err := parseGprmcStr(latStr)
	if err != nil {
		xlog.Error("parse latitude failed:", "err", err.Error())
		return "", "", err
	}
	longitude, err := parseGprmcStr(lngStr)
	if err != nil {
		xlog.Error("parse latitude failed:", "err", err.Error())
		return "", "", err
	}

	return latitude, longitude, nil

}
