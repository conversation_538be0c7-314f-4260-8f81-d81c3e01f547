package sl2022

import (
	"bytes"
	"encoding/hex"
	"errors"
	"fmt"
	"math"
	"net"
	"strconv"
	"strings"
	"time"

	"bs.com/ebox/pkg/now"
	"bs.com/ebox/pkg/xutils"
)

// 解析station_SN，合法则返回true
func IsValidSSN(ssn string) bool {
	if ssn == "" {
		return false
	}
	bufSn, err := xutils.Str2Hex(ssn)
	if err != nil {
		return false
	}
	if len(bufSn) != 5 {
		return false
	}
	return true
}

//BCD编码

// unix timestamp to local YYMMDD
// 时间,使用unix 秒
func time2Hex(ts int64) []byte {
	t := time.Unix(ts, 0)
	loc := now.Location()
	t = t.In(loc)
	y := t.Year()
	m := t.Month()
	d := t.Day()
	hh := t.Hour()
	mm := t.Minute()
	ss := t.Second()
	//xlog.Infof("%d-%02d-%02d %02d:%02d:%02d", y, m, d, hh, mm, ss)

	ret := make([]byte, 6)
	ret[0] = byte(y % 100)
	ret[1] = byte(m)
	ret[2] = byte(d)
	ret[3] = byte(hh)
	ret[4] = byte(mm)
	ret[5] = byte(ss)
	return ret
}

// unix time to YYMMDDHHmmSS
func time2str(ts int64) string {
	t := time.Unix(ts, 0)
	loc := now.Location()
	t = t.In(loc)
	y := t.Year()
	m := t.Month()
	d := t.Day()
	hh := t.Hour()
	mm := t.Minute()
	ss := t.Second()
	return fmt.Sprintf("%02d-%02d-%02d %02d:%02d:%02d", y%100, m, d, hh, mm, ss)
}

// unix timestamp to local YYMMDDHHmmSS
// 时间,使用unix 秒
func Timestamp2BCD(ts int64) []byte {
	t := time.Unix(ts, 0)
	loc := now.Location()

	t = t.In(loc)

	y := t.Year()
	m := t.Month()
	d := t.Day()
	hh := t.Hour()
	mm := t.Minute()
	ss := t.Second()
	fmt.Printf("%d-%02d-%02d %02d:%02d:%02d\n", y, m, d, hh, mm, ss)

	yBuf := Int2BCD(int64(y%100), N(2, 0))
	mBuf := Int2BCD(int64(m), N(2, 0))
	dBuf := Int2BCD(int64(d), N(2, 0))
	hhBuf := Int2BCD(int64(hh), N(2, 0))
	mmBuf := Int2BCD(int64(mm), N(2, 0))
	ssBuf := Int2BCD(int64(ss), N(2, 0))

	ret := make([]byte, 0)
	ret = append(ret, yBuf...)
	ret = append(ret, mBuf...)
	ret = append(ret, dBuf...)
	ret = append(ret, hhBuf...)
	ret = append(ret, mmBuf...)
	ret = append(ret, ssBuf...)

	//fmt.Println("len :", len(ret))
	return ret
}

// 时间,使用unix 秒
func Bcd2Time(buf []byte) (int64, error) {
	n := len(buf)
	if n < 4 {
		return 0, errors.New("invalid time bcd")
	}
	var y, m, d, hh, mm, ss int
	y = int(BCD2Int(buf[0:1]))
	m = int(BCD2Int(buf[1:2]))
	d = int(BCD2Int(buf[2:3]))
	hh = int(BCD2Int(buf[3:4]))

	if n > 4 {
		mm = int(BCD2Int(buf[4:5]))
	}

	if len(buf) > 5 {
		ss = int(BCD2Int(buf[5:6]))
	}
	loc := now.Location()
	tt := time.Date(2000+y, time.Month(m), d, hh, mm, ss, 0, loc)
	return tt.Unix(), nil
}

//“标识符”采用 2 字节 HEX 码。高位字节是标识符引导符;低位字节用于定义后续数据的字节数及其小数点后的位数
//此接口计算标识符的低位字节

// N x,y: BCD编码占用字节数和小数位。2个十进制数占用1个字节。整数的y 为 0
func N(x uint8, y uint8) uint8 {
	return ((x+1)/2)<<3 | (y & 0x07)
}

// 从size字段里面解析出来value所占字节长度
func SizeToLength(size uint8) int {
	return int(size >> 3)
}

// M x:占用字节长度，一般用于 hex 编码数据，或者说字符串。
func M(x uint8) uint8 {
	return (x << 3) & 0xF8
}

// BytesTrimRight 去掉定长存储的字符串的末尾的 0，右侧去0
func BytesTrimRight(buf []byte) string {
	i := bytes.IndexByte(buf, 0) //找到0的位置，截取
	buf = buf[:i]
	return string(buf)
}

// BytesTrimRight 去掉定长存储的byte数组的左边的0
func BytesTrimLeft(buf []byte) []byte {
	out := make([]byte, 0)
	for _, one := range buf {
		if len(out) == 0 && one == 0 {
			continue
		}
		out = append(out, one)
	}
	return out
}

// Float2BCD 浮点数的BCD编码，最高字节是符号位
func Float2BCD(val float64, size uint8) []byte {
	var buf1, buf2 []byte

	if val < 0 {
		buf1 = []byte{0xff}
		val = math.Abs(val)
	}

	d := size & 0x07 //小数位
	if d == 0 {
		//当d == 0 的时候，即无小数位
		buf2 = Int2BCD(int64(val), size)
	} else {
		num := val * math.Pow(10, float64(d))
		buf2 = Int2BCD(int64(num), size)
	}

	//负数前面有一个 0xFF的字节。
	return append(buf1, buf2...)
}

// 专门用于编码 SL2022的位移量等负数 —— 但是我们没有做 SL2022编码的工作，所以没有用到此接口
// Float2BCD 浮点数的BCD编码，最高字节是符号位
func Float2BCD2(val float64, size uint8) []byte {
	var buf1, buf2 []byte

	if val < 0 {
		buf1 = []byte{0xff}
		val = math.Abs(val)
	} else {
		buf1 = []byte{0x00}
	}

	d := size & 0x07 //小数位
	if d == 0 {
		//当d == 0 的时候，即无小数位
		buf2 = Int2BCD(int64(val), size)
	} else {
		num := val * math.Pow(10, float64(d))
		buf2 = Int2BCD(int64(num), size)
	}

	//负数前面有一个 0xFF的字节。
	return append(buf1, buf2...)
}

// BCD 编码解析成number
func BCD2Number(buf []byte, size uint8) any {
	d := size & 0x07
	if d == 0 {
		return BCD2Int(buf)
	}

	return BCD2Float(buf, size)
}

// SL2022的某些浮点数，负数是独占第一个字节的
func BCD2FloatS(buf []byte, size uint8) float64 {
	var num int64
	if buf[0] == 0xff {
		num = -1 * BCD2Int(buf[1:])
	} else {
		num = BCD2Int(buf[1:])
	}
	d := size & 0x07
	n := math.Pow(10, float64(d))
	return float64(num) / n
}

// sl2022 湖南协议处理负数：比如水位，负数的时候，数据前面多一个字节 0xFF，负数的 size 字段和非负数是一样
func BCD2Float(buf []byte, size uint8) float64 {
	var num int64
	if buf[0] == 0xff {
		num = -1 * BCD2Int(buf[1:])
	} else {
		num = BCD2Int(buf)
	}
	d := size & 0x07
	n := math.Pow(10, float64(d))
	return float64(num) / n
}

// BCD2Int 整数的BCD编码:高位补0
// []byte{0x12, 0x34}
func BCD2Int(buf []byte) int64 {
	//buf = BytesTrimLeft(buf)
	dst := make([]byte, 2*len(buf))
	hex.Encode(dst, buf)
	str := string(dst)
	//fmt.Println("str:", str)

	var ret int64 = 0
	for i := 0; i < len(str); i++ {
		c := int64(str[i] - '0')
		ret = ret*10 + c
	}

	return ret
}

// Int2BCD BCD编码：一位十进制数字，使用4bit来表达
// 其中size是SL651里面BCD的size表达方式，高5bit是buf字节数。根据size自动前缀补0
func Int2BCD(val int64, size uint8) (ret []byte) {
	buf := make([]byte, 0)

	//fmt.Println("val  : ",val)
	//fmt.Println("size : ",size>>3)
	//1、取数字的各个位，拼接字符串
	for {
		tmp := byte(val % 10)
		buf = append(buf, tmp+'0') //  '0' = 0x30
		val = val / 10
		if val == 0 {
			break
		}
	}

	l1 := len(buf)
	l2 := int(size>>3) * 2
	//fmt.Println("buf: ", string(buf),  l1 ,l2)

	if l1 < l2 {
		for i := 0; i < l2-l1; i++ {
			buf = append(buf, '0')
		}
	}

	//fmt.Println("str1 : ", string(buf))
	//2、逆序
	l := len(buf)
	//fmt.Println("len : ", l)
	if l >= 2 {
		for n := 0; n < l/2; n++ {
			buf[n], buf[l-n-1] = buf[l-n-1], buf[n]
		}
	}
	//3、转字节
	ret1, _ := xutils.Str2Hex(string(buf))

	//fmt.Println("ret1 : ", xutils.DumpHexRaw(ret1))
	ret = ret1[:size>>3]
	return
}

// Int2BCD BCD编码：一位十进制数字，使用4bit来表达
func Int2BCD444(val int64) (ret []byte) {
	buf := make([]byte, 0)

	//fmt.Println("val  : ",val)
	//fmt.Println("size : ",size>>3)
	//1、取数字的各个位，拼接字符串
	for {
		tmp := byte(val % 10)
		buf = append(buf, tmp+'0') //  '0' = 0x30
		val = val / 10
		if val == 0 {
			break
		}
	}

	//fmt.Println("str1 : ", string(buf))
	//2、逆序
	l := len(buf)
	//fmt.Println("len : ", l)
	if l >= 2 {
		for n := 0; n < l/2; n++ {
			buf[n], buf[l-n-1] = buf[l-n-1], buf[n]
		}
	}
	//3、转字节
	ret, _ = xutils.Str2Hex(string(buf))
	return
}

/*
func int2BCD222(val int64) (ret []byte) {
	numStr := strconv.FormatInt(val, 10)
	if len(numStr)%2 != 0 {
		numStr = fmt.Sprintf("0%s", numStr)
	}

	ret, _ = xutils.Str2Hex(numStr)
	return
}

func int2BCD333(val int64) (ret []byte) {
	ret = make([]byte, 0)
	numStr := strconv.FormatInt(val, 10)
	if len(numStr)%2 != 0 {
		numStr = fmt.Sprintf("0%s", numStr)
	}

	for i := 0; i < len(numStr); {
		tmp := ((numStr[i] - '0') << 4) | (numStr[i+1] - '0')
		ret = append(ret, tmp) // 0x30 是 '0'
		i += 2
	}

	return
}
*/

// IP2Bytes xxx.xxx.xxx.xxx
func IP2Bytes(ip string) []byte {
	arr := strings.Split(ip, ".")

	sb := strings.Builder{}
	for _, one := range arr {
		switch len(one) {
		case 1:
			sb.WriteString("00")
			sb.WriteString(one)
		case 2:
			sb.WriteString("0")
			sb.WriteString(one)
		case 3:
			sb.WriteString(one)
		}
	}
	numStr := sb.String()
	//fmt.Println("format ip string: ", numStr)

	var ret []byte
	for i := 0; i < len(numStr); {
		tmp := ((numStr[i] - '0') << 4) | (numStr[i+1] - '0')
		ret = append(ret, tmp) // 0x30 是 '0'
		i += 2
	}

	return ret
}

func Bytes2IP(buf []byte) string {
	dst := make([]byte, 2*len(buf))
	hex.Encode(dst, buf)

	str := string(dst)
	//fmt.Println("raw str:", str)

	sb := strings.Builder{}
	for i := 0; i < 4; i++ {
		one := str[i*3 : (i+1)*3]
		var oneIP string
		if one == "000" {
			oneIP = "0"
		} else {
			oneIP = strings.TrimLeft(one, "0")
		}
		sb.WriteString(oneIP)
		if i != 3 {
			sb.WriteByte('.')
		}
	}

	return sb.String()
}

// 返回 ip:端口
func Bytes2Host2(buf []byte) (host string, ok bool) {
	ip, port, ok := Bytes2Host(buf)
	if !ok {
		return "", false
	}
	return fmt.Sprintf("%s:%d", ip, port), true
}

// Port2Bytes 端口号，三个字节
func Port2Bytes(port uint16) []byte {
	return Int2BCD(int64(port), 3<<3)
}

func Bytes2Port(buf []byte) uint16 {
	return uint16(BCD2Int(buf))
}

// 参数检查
func isValidIP(ip string) bool {
	if net.ParseIP(ip) == nil {
		return false
	}
	return true
}
func isValidPort(p int) bool {
	if p > 65535 || p < 0 {
		return false
	}
	return true
}

func Host2Bytes(host string) ([]byte, error) {
	arr := strings.Split(host, ":")
	if len(arr) != 2 {
		return nil, errors.New("invalid host address")
	}
	ip := arr[0]
	port, err := strconv.Atoi(arr[1])
	if err != nil {
		return nil, err
	}
	if !isValidIP(ip) {
		return nil, errors.New("invalid ip")
	}
	if !isValidPort(port) {
		return nil, errors.New("invalid port")
	}

	//首字节是信道类型：1-短信，2-IPV4，3-北斗，4-海事卫 星，5-PSTN，6-超短波
	ret := []byte{0x02}
	b1 := IP2Bytes(ip)
	b2 := Port2Bytes(uint16(port))
	ret = append(ret, b1...)
	ret = append(ret, b2...)
	return ret, nil
}

func Bytes2Host(buf []byte) (ip string, port uint16, ok bool) {
	t := buf[0]
	switch t {
	case 2:
		//10字节：1字节信道类型，6字节IP地址，3字节端口号
		if len(buf) != 10 {
			ok = false
			return
		}
	default:
		ok = false
		return
	}
	return Bytes2IP(buf[1:7]), Bytes2Port(buf[7:10]), true
}

// utils
// 给定位图数组（映射在 mStatus 或者 mElement）， 转换成一个下标数组
// bitmap2Items
func bitmap2Items(buf []byte) []byte {
	// [D7 D6 D5 D4 D3 D2 D1 D0 , D15 D14 D13 D12 D11 D10 D9 D8, ... ... ...]
	var result []byte
	for i, b := range buf {
		for j := 0; j < 8; j++ {
			// if (b>>uint(7-j))&1 == 1 {
			if (b>>j)&1 == 1 {
				result = append(result, byte(i*8+j))
			}
		}
	}
	return result
}

// 将下标数组，转换成 N 个字节的位图
// buf是下标数组，元素取值范围 0 ~ 8*cnt，不可以重复
func items2Bitmap(buf []byte, cnt int) []byte {
	// [D7 D6 D5 D4 D3 D2 D1 D0 , D15 D14 D13 D12 D11 D10 D9 D8, ... ... ...]
	result := []byte{}
	for range cnt {
		result = append(result, 0)
	}
	for _, v := range buf {
		idx := v / 8
		result[idx] |= 1 << (v % 8)
	}
	return result
}

func Uint16ToHex(val uint16) []byte {
	buf := make([]byte, 2)
	DefaultEndian.PutUint16(buf, val)
	return buf
}
func Uint32ToHex(val uint32) []byte {
	buf := make([]byte, 4)
	DefaultEndian.PutUint32(buf, val)
	return buf
}

func Hex2Uint16(val []byte) uint16 {
	return DefaultEndian.Uint16(val)
}
func Hex2Uint32(val []byte) uint32 {
	return DefaultEndian.Uint32(val)
}

func HexStr2Byte(hexStr string) ([]byte, error) {
	// 去掉空格和 前缀"0x"
	hexStr = strings.TrimSpace(hexStr)
	hexStr = strings.TrimPrefix(hexStr, "0x")

	// 解析为 []byte
	return hex.DecodeString(hexStr)
}

// 对于标准 651 协议，采集要素使用位图标识
// 表D.2 遥测站监测要素定义表
// 用 8 个字节，一共 64 个 bit 实现一个 bitmap，来标记采集要素。素对应数据位置“1”表示监测该要素，置“0”表示不监测。
// 编码：arr 是一个序号数组，返回编码结果
func IndexArrayToBitmap(arr []int) []byte {
	bitmap := make([]byte, 8) // Create an array of 8 bytes, each initialized to 0

	for _, num := range arr {
		if num >= 0 && num < 64 {
			byteIndex := num / 8
			bitIndex := num % 8
			bitmap[byteIndex] |= 1 << (7 - bitIndex) // Set the corresponding bit
		}
	}

	return bitmap
}

// 解码
func BitmapToIndexArray(buf []byte) []int {
	var arr []int
	for byteIndex, b := range buf {
		for bitIndex := 0; bitIndex < 8; bitIndex++ {
			if b&(1<<(7-bitIndex)) != 0 {
				arr = append(arr, byteIndex*8+bitIndex)
			}
		}
	}

	return arr
}
