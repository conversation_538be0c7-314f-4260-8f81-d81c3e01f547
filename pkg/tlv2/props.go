package tlv2

import (
	"errors"
	"fmt"
	"sync"

	"bs.com/ebox/pkg/xlog"
)

// product 信息也依赖此定义
// 自解释的属性定义
// 任何一个属性有如下三个信息：protocol 中对应的id（uint16），key，type，unit  desc
// metadata: 关于属性值的辅助信息。比如:
// 枚举类型的取值范围
// 数值类型的范围，精度，增量，计量单位等。参考 y= kx+m， 所有浮点数和小数都转为无符号整数

type PropInfo struct {
	Tag     uint8  `json:"id"`       // 属性的键，uint8, 必须保证唯一
	Key     string `json:"key"`      // 属性解析成键值对的键名
	EncType uint8  `json:"dataType"` // 编码数据类型
	Desc    string `json:"desc"`     // 属性的描述，注释
}

// 协议版本
// Version1：包含 TTLV，tag，type，length，value
// Version2：包含 T[L]V，tag，[length]，value。比 version1省略了 type 和定长 length，因为这两个信息在 tag 描述里面可以查询到

// 根据 key 或者 ID 来检索 prop
type PropManager struct {
	TagMap map[uint8]*PropInfo  // 属性以 tag 为索引
	KeyMap map[string]*PropInfo // 属性以 key 为索引
	sync.RWMutex
}

var manager *PropManager

func init() {
	manager = &PropManager{
		TagMap: make(map[uint8]*PropInfo),
		KeyMap: make(map[string]*PropInfo),
	}

	addTlv2BaseProps()
	addTlv2SensorProps()
	addDemoProps()
}

func GetPropByID(ID uint8) (*PropInfo, error) {
	manager.RLock()
	defer manager.RUnlock()

	p, ok := manager.TagMap[ID]
	if !ok {
		xlog.Infof("invalid prop id: %v", ID)
		return nil, errors.New(fmt.Sprintf("invalid prop id: %v", ID))
	} else {
		return p, nil
	}
}

func GetPropByKey(k string) (*PropInfo, error) {
	manager.RLock()
	defer manager.RUnlock()

	p, ok := manager.KeyMap[k]
	if !ok {
		return nil, errors.New("invalid prop key")
	} else {
		return p, nil
	}
}

func AddProps(props ...*PropInfo) {
	for _, p := range props {
		manager.Lock()
		manager.TagMap[p.Tag] = p
		manager.KeyMap[p.Key] = p
		manager.Unlock()
	}
}

func addTlv2BaseProps() {
	// 设备基本信息
	did := &PropInfo{Tag: 0x00, Key: "did", EncType: STRING}
	// 产品 ID
	pid := &PropInfo{Tag: 0x01, Key: "pid", EncType: STRING}
	// 时间戳
	hwID := &PropInfo{Tag: 0x02, Key: "hwID", EncType: STRING}
	imei := &PropInfo{Tag: 0x03, Key: "imei", EncType: STRING}
	iccid := &PropInfo{Tag: 0x04, Key: "iccid", EncType: STRING}
	gprmc := &PropInfo{Tag: 0x05, Key: "gprmc", EncType: STRING}
	AddProps(did, pid, hwID, imei, iccid, gprmc)

	// 设备版本号，密钥
	fwVersion := &PropInfo{Tag: 0x06, Key: "fwVersion", EncType: STRING}
	hwVersion := &PropInfo{Tag: 0x07, Key: "hwVersion", EncType: STRING}
	secret := &PropInfo{Tag: 0x08, Key: "secret", EncType: STRING}
	AddProps(fwVersion, hwVersion, secret)

	// 时间戳
	ts := &PropInfo{Tag: 0x09, Key: "ts", EncType: INT64}
	AddProps(ts)

	// 文件传输相关
	fileSlice := &PropInfo{Tag: 0x0a, Key: "fileSlice", EncType: BYTES}
	fileID := &PropInfo{Tag: 0x0b, Key: "fileID", EncType: STRING}
	crc16 := &PropInfo{Tag: 0x0c, Key: "crc16", EncType: UINT16}
	total := &PropInfo{Tag: 0x0d, Key: "total", EncType: UINT32}
	offset := &PropInfo{Tag: 0x0e, Key: "offset", EncType: UINT32}
	length := &PropInfo{Tag: 0x0f, Key: "length", EncType: UINT16}
	AddProps(fileSlice, fileID, crc16, total, offset, length)

	code := &PropInfo{Tag: 0x10, Key: "code", EncType: UINT16}
	message := &PropInfo{Tag: 0x11, Key: "errMsg", EncType: STRING}
	log1 := &PropInfo{Tag: 0x12, Key: "log", EncType: STRING}
	rebootTs := &PropInfo{Tag: 0x13, Key: "reboot_ts", EncType: INT64} // 设备最近重启时间
	status := &PropInfo{Tag: 0x14, Key: "status", EncType: UINT8}      //0：离线，1：在线，2：低功耗模式,3：故障

	AddProps(code, message, log1, rebootTs, status)

	logLevel := &PropInfo{Tag: 0x15, Key: "logLevel", EncType: STRING}
	AddProps(logLevel)

	//
	name := &PropInfo{Tag: 0x16, Key: "name", EncType: STRING}
	lng := &PropInfo{Tag: 0x17, Key: "lng", EncType: STRING}
	lat := &PropInfo{Tag: 0x18, Key: "lat", EncType: STRING}
	checksum := &PropInfo{Tag: 0x19, Key: "checksum", EncType: UINT32}
	AddProps(name, lng, lat, checksum)

}

func addTlv2SensorProps() {
	rssi := &PropInfo{Tag: 0x81, Key: "rssi", EncType: UINT8}
	chgVolt := &PropInfo{Tag: 0x82, Key: "chgVolt", EncType: FLOAT32}
	batVolt := &PropInfo{Tag: 0x83, Key: "batVolt", EncType: FLOAT32}
	temperature := &PropInfo{Tag: 0x84, Key: "temperature", EncType: FLOAT32}
	humidity := &PropInfo{Tag: 0x85, Key: "humidity", EncType: UINT8}
	yuLiang := &PropInfo{Tag: 0x86, Key: "yuLiang", EncType: TD2}
	shuiWei := &PropInfo{Tag: 0x87, Key: "shuiWei", EncType: TD4}
	tuXiang := &PropInfo{Tag: 0x88, Key: "tuXiang", EncType: BYTES}

	AddProps(rssi, chgVolt, batVolt, temperature, humidity, yuLiang, shuiWei, tuXiang)
}

// 添加测试用数据
// 参考 message_test.go
func addDemoProps() {

	k01 := &PropInfo{Tag: 0xd0, Key: "k01", EncType: STRING}
	k02 := &PropInfo{Tag: 0xd1, Key: "k02", EncType: FLOAT64}
	k03 := &PropInfo{Tag: 0xd2, Key: "k03", EncType: FLOAT32}
	k04 := &PropInfo{Tag: 0xd3, Key: "k04", EncType: INT64}
	k05 := &PropInfo{Tag: 0xd4, Key: "k05", EncType: UINT64}
	k06 := &PropInfo{Tag: 0xd5, Key: "k06", EncType: UINT32}
	k07 := &PropInfo{Tag: 0xd6, Key: "k07", EncType: INT32}
	k08 := &PropInfo{Tag: 0xd7, Key: "k08", EncType: INT16}
	k09 := &PropInfo{Tag: 0xd8, Key: "k09", EncType: UINT16}
	k10 := &PropInfo{Tag: 0xd9, Key: "k10", EncType: INT8}
	k11 := &PropInfo{Tag: 0xda, Key: "k11", EncType: UINT8}
	k12 := &PropInfo{Tag: 0xdb, Key: "k12", EncType: BYTES}
	k13 := &PropInfo{Tag: 0xdc, Key: "k13", EncType: BOOL}

	AddProps(k01, k02, k03, k04, k05, k06, k07, k08, k09, k10, k11, k12, k13)

	k14 := &PropInfo{Tag: 0xdd, Key: "k14", EncType: TD1}
	k15 := &PropInfo{Tag: 0xde, Key: "k15", EncType: TD2}
	k17 := &PropInfo{Tag: 0xdf, Key: "k17", EncType: TD4}
	AddProps(k14, k15, k17)

	k16 := &PropInfo{Tag: 0xe0, Key: "k16", EncType: KV}
	AddProps(k16)
}
