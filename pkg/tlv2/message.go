package tlv2

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	"bs.com/ebox/pkg/xbin"
	"bs.com/ebox/pkg/xutils"
)

type Message struct {
	Config  uint8
	Version uint8
	MsgType uint8
	Data    map[string]interface{}
}

func (m *Message) String1() string {
	sb := strings.Builder{}
	sb.WriteString("[\n")
	sb.WriteString(fmt.Sprintf("\tConfig   : 0x%02x\n", m.Config))
	sb.WriteString(fmt.Sprintf("\tVersion  : 0x%02x\n", m.Version))
	sb.WriteString(fmt.Sprintf("\tMsgType  : 0x%02x\n", m.MsgType))
	sb.WriteString(fmt.Sprintf("\tData     : %s\n", DumpMap(m.Data)))
	sb.WriteString("]\n")
	return sb.String()
}

func (m *Message) String() string {
	return xutils.JSONString(m)
}

// 解析二进制协议的时候用到
func DumpMap(m map[string]interface{}) string {

	sortedKeys := make([]string, 0)
	for k1 := range m {
		sortedKeys = append(sortedKeys, k1)
	}

	// sort 'string' key in increasing order
	sort.Strings(sortedKeys)

	sb := strings.Builder{}
	sb.Reset()

	// sb.WriteString(fmt.Sprintf("map len: %d\n", len(m)))
	sb.WriteString("Body: [\n")
	for _, k := range sortedKeys {
		var out string
		v := m[k]
		switch v.(type) {
		case float64:
			out = strconv.FormatFloat(v.(float64), 'f', -1, 64)
		case float32:
			out = strconv.FormatFloat(float64(v.(float32)), 'f', -1, 32)
		case int8:
			// out = strconv.FormatInt(int64(v.(int8)), 16)
			// out = fmt.Sprintf("0x%02x", v.(int8))
			out = fmt.Sprintf("%d", v.(int8))
		case int16:
			// out = strconv.FormatInt(int64(v.(int16)), 16)
			// out = fmt.Sprintf("0x%04x", v.(int16))
			out = fmt.Sprintf("%d", v.(int16))
		case int32:
			// out = strconv.FormatInt(int64(v.(int32)), 16)
			// out = fmt.Sprintf("0x%08x", v.(int32))
			out = fmt.Sprintf("%d", v.(int32))
		case int64:
			// out = strconv.FormatInt(v.(int64), 16)
			// out = fmt.Sprintf("0x%016x", v.(int64))
			out = fmt.Sprintf("%d", v.(int64))
		case uint8:
			// out = strconv.FormatUint(uint64(v.(uint8)), 16)
			// out = fmt.Sprintf("0x%02x", v.(uint8))
			out = fmt.Sprintf("%d", v.(uint8))
		case bool:
			out = strconv.FormatBool(v.(bool))
		case uint16:
			// out = strconv.FormatUint(uint64(v.(uint16)), 16)
			// out = fmt.Sprintf("0x%04x", v.(uint16))
			out = fmt.Sprintf("%d", v.(uint16))
		case uint32:
			// out = strconv.FormatUint(uint64(v.(uint32)), 16)
			// out = fmt.Sprintf("0x%08x", v.(uint32))
			out = fmt.Sprintf("%d", v.(uint32))
		case uint64:
			// out = strconv.FormatUint(v.(uint64), 16)
			// out = fmt.Sprintf("0x%016x", v.(uint64))
			out = fmt.Sprintf("%d", v.(uint64))
		case []byte:
			// out = hex.Dump(v.([]byte))
			out = xutils.DumpHexRaw(v.([]byte))
		case string:
			out = v.(string)
		case []float32:
			arr := v.([]float32)
			out = "[ "
			for _, one := range arr {
				out += strconv.FormatFloat(float64(one), 'f', -1, 32)
				out += " "
			}
			out += "]"
		case []uint16:
			arr := v.([]uint16)
			out = "[ "
			for _, one := range arr {
				out += fmt.Sprintf("0x%04x", one)
				out += " "
			}
			out += " ]"

		case TData1:
			for _, one := range v.(TData1) {
				sb.WriteString(fmt.Sprintf("ts:%d, value:%d\n", one.TS, one.Value))
			}

		case TData2:
			for _, one := range v.(TData2) {
				sb.WriteString(fmt.Sprintf("ts:%d, value:%d\n", one.TS, one.Value))
			}

		default:
			out = "not impl"
		}

		var kv string
		if k == "slice" {
			kv = fmt.Sprintf("\t%s : %s\n", k, "[ ... firmware slice ... ]")
		} else {
			kv = fmt.Sprintf("\t%s : %s\n", k, out)
		}
		sb.WriteString(kv)
	}
	sb.WriteString("]")

	return sb.String()
}

func Encode(m *Message) ([]byte, error) {
	p := NewPacket()
	p.SetConfig(m.Config)
	p.SetType(m.MsgType)
	p.SetVersion(m.Version)
	p.Body = m.Data

	return xbin.Encode(p, DefaultByteOrderTLV)
}

func Decode(buf []byte) (*Message, error) {

	p := &Packet{}
	err := xbin.Decode(buf, p, DefaultByteOrderTLV)
	if err != nil {
		return nil, err
	}

	m := &Message{
		Config:  p.Config,
		Version: p.GetVersion(),
		MsgType: p.GetType(),
		Data:    p.Body,
	}
	return m, nil
}
