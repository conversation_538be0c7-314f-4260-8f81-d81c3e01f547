package tlv2

import (
	"errors"
	"fmt"

	"bs.com/ebox/pkg/xutils"
)

// crc16

type CRCField struct {
	StartOffset int
}

func (f *CRCField) SaveOffset(in int) {
	f.StartOffset = in
}

// not used
func (f *CRCField) PushReserveSize() int {
	return -1
}

func (f *CRCField) PopReserveSize() int {
	return 2
}

func (f *CRCField) Fill(curOffset int, buf []byte) error {
	ret, err := xutils.CRC16IBM5(buf[f.StartOffset:curOffset])
	if err != nil {
		return err
	}

	DefaultByteOrderTLV.PutUint16(buf[curOffset:], ret)
	return nil
}

func (f *CRCField) Check(curOffset int, buf []byte) error {

	ret, err := xutils.CRC16IBM5(buf[f.StartOffset:curOffset])
	if err != nil {
		return err
	}
	if ret != DefaultByteOrderTLV.Uint16(buf[curOffset:]) {
		str := fmt.Sprintf("crc16 should be: 0x%x, but we get: 0x%x", DefaultByteOrderTLV.Uint16(buf[curOffset:]), ret)
		return errors.New("crc didn't match, " + str)
	}

	return nil
}
