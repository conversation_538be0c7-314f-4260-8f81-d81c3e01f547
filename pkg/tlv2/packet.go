package tlv2

import (
	"errors"
	"fmt"
	"strings"

	"bs.com/ebox/pkg/xlog"
	"bs.com/ebox/pkg/xutils"

	"bs.com/ebox/pkg/xbin"
)

// TODO: Magic定义为 byte array,固定大小端。以方便解析。

// Begin:
type Packet struct {
	Begin   uint8                  // 包头标志
	Config  uint8                  // 包参数
	Version uint8                  // 版本
	MsgType uint8                  // 消息类型
	BodyLen uint16                 // 包体长度
	Body    map[string]interface{} // 包体
	CRC16   uint16                 // 包体校验
	End     uint8                  // 包尾标识
}

// TODO:修改包头必须修改此接口
func HeadSize() int {
	return 5
}
func NewPacket() *Packet {
	return &Packet{
		Begin: 0x68,
		End:   0x20,
	}
}
func (h *Packet) String() string {
	sb := strings.Builder{}
	sb.WriteString("[\n")
	sb.WriteString(fmt.Sprintf("\tMagic    : 0x%02x\n", h.Begin))
	sb.WriteString(fmt.Sprintf("\tVersion  : 0x%02x\n", h.Version))
	sb.WriteString(fmt.Sprintf("\tMsgType  : 0x%02x\n", h.MsgType))
	sb.WriteString(fmt.Sprintf("\tBodyLen  : 0x%04x\n", h.BodyLen))
	sb.WriteString(fmt.Sprintf("\tBody     : [...]\n"))
	sb.WriteString(fmt.Sprintf("\tChecksum : 0x%04x\n", h.CRC16))
	sb.WriteString(fmt.Sprintf("\tEnd      : 0x%02x\n", h.End))
	sb.WriteString("]\n")
	return sb.String()
}

// 包参数
func (h *Packet) SetConfig(c uint8) *Packet {
	h.Config = c
	return h
}

func (p *Packet) GetConfig() uint8 {
	return p.Config
}

// 版本号
func (h *Packet) SetVersion(ver uint8) *Packet {
	h.Version = ver
	return h
}

func (p *Packet) GetVersion() uint8 {
	return p.Version
}

// 包类型
func (h *Packet) SetType(t uint8) *Packet {
	h.MsgType = t
	return h
}

func (p *Packet) GetType() uint8 {
	return p.MsgType
}

// 对于定长数据，省略掉 length
func (p *Packet) Encode(e xbin.PacketEncoder) (err error) {

	// 包头
	e.PutUint8(p.Begin)
	e.PutUint8(p.Config)
	e.PutUint8(p.Version)
	e.PutUint8(p.MsgType)
	e.Push(&LengthField{})
	e.Push(&CRCField{})

	version := p.Version
	// 包体
	for k, v := range p.Body {
		prop, err := GetPropByKey(k)
		if err != nil {
			xlog.Error("ignore invalid prop:", k)
			continue
		}

		obj := TlvItem{
			Key:     k,
			Value:   v,
			Tag:     prop.Tag,
			EncType: prop.EncType,
			Length:  0,
			Version: version,
		}

		err = obj.Encode(e)
		if err != nil {
			xlog.Error("ignore tlv encode failed", "err", err.Error())
			continue
		}
	}
	// 包尾
	e.Pop() // CRC16
	e.Pop() // body length

	e.PutUint8(p.End)
	return
}

func (p *Packet) Decode(d xbin.PacketDecoder) (err error) {

	// 捕获编码过程中的异常
	defer func() {
		xutils.Recovery("tlv2-decode")
	}()

	// 包头
	p.Begin, err = d.Uint8()
	if err != nil {
		return err
	}
	// 检查包头
	if p.Begin != 0x68 {
		return errors.New("invalid packet head")
	}
	p.Config, err = d.Uint8()
	if err != nil {
		return err
	}

	p.Version, err = d.Uint8()
	if err != nil {
		return err
	}

	p.MsgType, err = d.Uint8()
	if err != nil {
		return err
	}
	p.BodyLen, err = d.Uint16()
	if err != nil {
		return err
	}

	// 此处开始计算 body 的 crc16
	err = d.Push(&CRCField{})
	if err != nil {
		return err
	}

	//log.Printf("remain: %d, body len: %d\n", d.Remaining(), p.BodyLen)

	if uint16(d.Remaining()) < (p.BodyLen + 3) {
		return errors.New("insufficient data to decode packet, more bytes expected")
	}
	version := p.Version
	p.Body = make(map[string]interface{})
	for {
		// 解析完成，退出
		if uint16(d.Remaining()) <= 3 {
			break
		}
		one := &TlvItem{
			Version: version,
		}
		err = one.Decode(d)
		if err != nil {
			return err
		}

		prop, err := GetPropByID(one.Tag)
		if err != nil {
			continue
		}
		p.Body[prop.Key] = one.Value
		//log.Printf("tlv decode ok,tag: 0x%x, remain: %d", one.Tag, d.Remaining())
	}

	// 包尾
	err = d.Pop() // CRC16
	if err != nil {
		return err
	}
	p.End, err = d.Uint8()
	if err != nil {
		return err
	}
	return nil
}
