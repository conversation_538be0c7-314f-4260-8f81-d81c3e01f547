package tlv2

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"bs.com/ebox/pkg/xbin"
	"bs.com/ebox/pkg/xlog"
)

type TlvItem struct {
	Key     string
	Tag     uint8
	EncType uint8
	Length  uint16
	Value   interface{}
	Version uint8
}

func (t *TlvItem) TypeString() string {
	return TypeGetNameByID(t.EncType)
}

func (t *TlvItem) String() string {
	sb := strings.Builder{}
	sb.WriteString(fmt.Sprintf("Key : \t%s\n", t.Key))
	sb.WriteString(fmt.Sprintf("Tag : \t0x%04x\n", t.Tag))
	sb.WriteString(fmt.Sprintf("Type : \t%s\n", t.TypeString()))
	sb.WriteString(fmt.Sprintf("Length : \t%d\n", t.Length))
	sb.WriteString(fmt.Sprintf("value: \t%v\n", t.Value))

	return sb.String()
}

func (t *TlvItem) Encode(e xbin.PacketEncoder) (err error) {
	info := GetTypeByID(t.EncType)
	if info.Size != 0 {
		t.Length = info.Size
	}

	e.PutUint8(t.Tag)
	// version 0 :不省略 type 和 length
	if t.Version == 0 {
		e.PutUint8(t.EncType)

		// 定长的也要有长度信息（PacketEncoder里面默认定长数据无长度编码）
		// 变长数据的长度信息由 PacketEncoder 默认实现
		if info.Size != 0 {
			e.PutUint16(info.Size)
		}
	}

	value := t.Value
	switch t.EncType {
	case BOOL:
		if val, ok := value.(bool); ok {
			e.PutBool(val)
		} else {
			err = errors.New("wrong bool value")
		}
	case INT8:
		if val, ok := value.(int8); ok {
			e.PutInt8(val)
		} else if val2, ok := value.(float64); ok {
			e.PutInt8(int8(val2))
		} else {
			err = errors.New("wrong int8 value")
		}
	case UINT8:
		if val, ok := value.(uint8); ok {
			e.PutUint8(val)
		} else if val2, ok := value.(float64); ok {
			e.PutUint8(uint8(val2))
		} else {
			err = errors.New("wrong uint8 value")
		}
	case INT16:
		if val, ok := value.(int16); ok {
			e.PutInt16(val)
		} else if val2, ok := value.(float64); ok {
			e.PutInt16(int16(val2))
		} else {
			err = errors.New("wrong int16 value")
		}
	case UINT16:
		if val, ok := value.(uint16); ok {
			e.PutUint16(val)
		} else if val2, ok := value.(float64); ok {
			e.PutUint16(uint16(val2))
		} else {
			err = errors.New("wrong uint16 value")
		}

	case INT32:
		if val, ok := value.(int32); ok {
			e.PutInt32(val)
		} else if val2, ok := value.(float64); ok {
			e.PutInt32(int32(val2))
		} else {
			err = errors.New("wrong int32 value")
		}

	case UINT32:
		if val, ok := value.(uint32); ok {
			e.PutUint32(val)
		} else if val2, ok := value.(float64); ok {
			e.PutUint32(uint32(val2))
		} else {
			err = errors.New("wrong uint32 value")
		}

	case INT64:
		if val, ok := value.(int64); ok {
			e.PutInt64(val)
		} else if val2, ok := value.(float64); ok {
			e.PutInt64(int64(val2))
		} else {
			err = errors.New("wrong int64 value")
		}

	case UINT64:
		if val, ok := value.(uint64); ok {
			e.PutUint64(val)
		} else if val2, ok := value.(float64); ok {
			e.PutUint64(uint64(val2))
		} else {
			err = errors.New("wrong uint64 value")
		}

	case FLOAT32:
		if val, ok := value.(float32); ok {
			e.PutFloat32(val)
		} else if val2, ok := value.(float64); ok {
			e.PutFloat32(float32(val2))
		} else {
			err = errors.New("wrong float32 value")
		}

	case FLOAT64:
		if val, ok := value.(float64); ok {
			e.PutFloat64(val)
		} else {
			err = errors.New("wrong float64 value")
		}

		// 变长数据的长度信息默认就有
	case STRING:
		if val, ok := value.(string); ok {
			t.Length = uint16(len(val))
			e.PutString(val)
		} else {
			err = errors.New("wrong string value")
		}

	case BYTES:
		if val1, ok1 := value.([]byte); ok1 {
			t.Length = uint16(len(val1))
			e.PutBytes(val1)
		} else if val2, ok2 := value.(string); ok2 {
			//json 序列化之后，[]byte 会通过 base64编码，转为字符串
			dst, err := base64.StdEncoding.DecodeString(val2)
			if err != nil {
				xlog.Error("parse string to []byte failed:", "err", err.Error())
				err = errors.New("wrong bytes value")
			} else {
				t.Length = uint16(len(dst))
				e.PutBytes(dst)
			}
		}

	case TD1:
		data, ok := value.(TData1)
		if !ok {
			//json 序列化，丢失类型
			jBuf, err := json.Marshal(value)
			if err != nil {
				xlog.Error("marshal err: ", err.Error())
				return errors.New("wrong TData1 data")
			}
			data = TData1{}
			err = json.Unmarshal(jBuf, &data)
			if err != nil {
				xlog.Error("unmarshal error :", err.Error())
				return errors.New("wrong TData1 data")
			}
		}
		t.Length = uint16(len(data))
		e.PutUint16(uint16(len(data)))
		for _, one := range data {
			e.PutInt64(one.TS)
			e.PutUint8(one.Value)
		}

	case TD2:
		data, ok := value.(TData2)
		if !ok {
			jBuf, err := json.Marshal(value)
			if err != nil {
				xlog.Error("marshal err: ", err.Error())
				return errors.New("wrong TData1 data")
			}
			data = TData2{}
			err = json.Unmarshal(jBuf, &data)
			if err != nil {
				xlog.Error("unmarshal error :", err.Error())
				return errors.New("wrong TData1 data")
			}
		}
		t.Length = uint16(len(data))
		e.PutUint16(uint16(len(data)))
		for _, one := range data {
			e.PutInt64(one.TS)
			e.PutUint16(one.Value)
		}

	case TD4:
		data, ok := value.(TData4)
		if !ok {
			jBuf, err := json.Marshal(value)
			if err != nil {
				xlog.Error("marshal err: ", err.Error())
				return errors.New("wrong TData1 data")
			}
			data = TData4{}
			err = json.Unmarshal(jBuf, &data)
			if err != nil {
				xlog.Error("unmarshal error :", err.Error())
				return errors.New("wrong TData1 data")
			}
		}
		t.Length = uint16(len(data))
		e.PutUint16(uint16(len(data)))
		for _, one := range data {
			e.PutInt64(one.TS)
			e.PutUint32(one.Value)
		}

	case KV:
		//格式："k1:v1,k2:v2"
		data, ok := value.(KVMap)

		if !ok {
			jBuf, err := json.Marshal(value)
			if err != nil {
				xlog.Error("marshal err: ", err.Error())
				break
			}

			data = KVMap{}
			err = json.Unmarshal(jBuf, &data)
			if err != nil {
				xlog.Error("unmarshal error :", err.Error())
				break
			}
		}

		sb := strings.Builder{}
		for k, v := range data {
			sb.WriteString(k)
			sb.WriteString(":")
			sb.WriteString(v)
			sb.WriteString(",")
		}

		out := strings.TrimRight(sb.String(), ",")
		//长度信息等于字符串的信息
		t.Length = uint16(len(out))
		e.PutString(out)

	default:
		xlog.Info("invalid prop enc type1: ", t)
		e := fmt.Sprintf("unsuport type, value2: %v, type : %s", value, reflect.TypeOf(value).String())
		err = errors.New(e)
	}

	//xlog.Info("encode tlv obj :", utils.JSONString(t))

	//错误处理
	if err != nil {
		xlog.Info("encode failed: ", err.Error())
		return err
	}

	return nil
}

func (t *TlvItem) Decode(d xbin.PacketDecoder) (err error) {

	t.Tag, err = d.Uint8()
	if err != nil {
		return err
	}

	if t.Version == 0 {
		// version 0 :不省略 type
		t.EncType, err = d.Uint8()
		if err != nil {
			return err
		}

		// version == 0, 定长不省略 length 信息
		t.Length, err = d.Uint16()
		if err != nil {
			return err
		}
	} else if t.Version == 1 {
		// version 1 :省略 type
		// 不定长的类型，获取 length
		info := GetTypeByID(t.EncType)
		if info.Size == 0 {
			t.Length, err = d.Uint16()
			if err != nil {
				return err
			}
		}
	}

	switch t.EncType {
	case BOOL:
		t.Value, err = d.Bool()
	case INT8:
		t.Value, err = d.Int8()
	case UINT8:
		t.Value, err = d.Uint8()
	case INT16:
		t.Value, err = d.Int16()
	case UINT16:
		t.Value, err = d.Uint16()
	case INT32:
		t.Value, err = d.Int32()
	case UINT32:
		t.Value, err = d.Uint32()
	case INT64:
		t.Value, err = d.Int64()
	case UINT64:
		t.Value, err = d.Uint64()
	case FLOAT32:
		t.Value, err = d.Float32()
	case FLOAT64:
		t.Value, err = d.Float64()
	case STRING:
		t.Value, err = d.StringWithLen(int(t.Length))
	case BYTES:
		t.Value, err = d.BytesWithLen(int(t.Length))
	case TD1:
		//out := TData1{}
		out := make(TData1, 0)
		var i uint16 = 0
		for ; i < t.Length; i++ {
			tmp := OneTD1{}
			tmp.TS, err = d.Int64()
			if err != nil {
				break
			}
			tmp.Value, err = d.Uint8()
			if err != nil {
				break
			}
			out = append(out, tmp)
		}
		t.Value = out
	case TD2:
		//out := TData2{}
		out := make(TData2, 0)
		var i uint16 = 0
		for ; i < t.Length; i++ {
			tmp := OneTD2{}
			tmp.TS, err = d.Int64()
			if err != nil {
				break
			}
			tmp.Value, err = d.Uint16()
			if err != nil {
				break
			}
			out = append(out, tmp)
		}
		t.Value = out

	case TD4:
		out := make(TData4, 0)
		var i uint16 = 0
		for ; i < t.Length; i++ {
			tmp := OneTD4{}
			tmp.TS, err = d.Int64()
			if err != nil {
				break
			}
			tmp.Value, err = d.Uint32()
			if err != nil {
				break
			}
			out = append(out, tmp)
		}
		t.Value = out

	case KV:
		out := KVMap{}
		str, err := d.StringWithLen(int(t.Length))
		if err != nil {
			xlog.Error("parse kv string failed:" + err.Error())
			break
		}
		arr1 := strings.Split(str, ",")
		for _, one := range arr1 {
			arr2 := strings.Split(one, ":")
			if len(arr2) == 2 {
				out[arr2[0]] = arr2[1]
			} else {
				xlog.Error("ignore parse kv string error")
				continue
			}
		}
		t.Value = out

	default:
		xlog.Info("tlv obj", t)
		return errors.New("tlv decode:invalid prop enc type")
	}

	//错误处理
	if err != nil {
		xlog.Info("encode failed: ", err.Error())
		return err
	}

	return nil
}
