package xmqtt

import (
	"crypto/tls"
	"net/url"
	"time"

	"bs.com/ebox/pkg/xlog"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

func NewClientSimple(host, clientID, username, password string, heartbeatTimeout int) mqtt.Client {
	opts := mqtt.NewClientOptions()
	opts.AddBroker(host)
	opts.SetClientID(clientID)
	opts.SetUsername(username)
	opts.SetPassword(password)
	opts.SetKeepAlive(time.Duration(heartbeatTimeout) * time.Second)      // 心跳间隔60秒
	opts.SetAutoReconnect(true).SetMaxReconnectInterval(30 * time.Second) // 意外离线的重连参数
	// opts.SetConnectRetry(true).SetConnectRetryInterval(5 * time.Second)   // 首次连接的重连参数
	opts.SetConnectRetry(false)           // 首次不支持重连，超时即退出
	opts.SetOrderMatters(false)           // 不需要消息有序
	opts.SetPingTimeout(3 * time.Second)  // PING 超时时间
	opts.SetWriteTimeout(3 * time.Second) // 写超时时间

	// opts.SetCleanSession(false) //支持离线消息

	//重新连接尝试之间等待的最大时间
	opts.SetOnConnectHandler(func(client mqtt.Client) {
		xlog.Info("mqtt connected successfully", "clientID", clientID)
	})
	opts.SetReconnectingHandler(func(client mqtt.Client, opt *mqtt.ClientOptions) {
		xlog.Info("mqtt reconnecting...", "clientID", clientID)
	})

	opts.SetConnectionAttemptHandler(func(broker *url.URL, tlsCfg *tls.Config) *tls.Config {
		xlog.Info("mqtt attempt connecting", "url", broker.String())
		return tlsCfg
	})
	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		xlog.Warn("mqtt connection lost", "err", err, "clientID", clientID)
	})

	// TODO：设置遗嘱消息
	opts.SetWill("/will/", "bye.....", 0, false)

	return mqtt.NewClient(opts)
}
