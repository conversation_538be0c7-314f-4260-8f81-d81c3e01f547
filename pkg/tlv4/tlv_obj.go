package tlv4

import (
	"encoding/base64"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"bs.com/ebox/pkg/bintool"
	"bs.com/ebox/pkg/xlog"

	"bs.com/ebox/pkg/xbin"
	"bs.com/ebox/pkg/xutils"
)

/*
+----------------------------+---------------------------+----------------------------------------------------+
|          Tag               |        Length             |                    Value                           |
+----------------------------+---------------------------+----------------------------------------------------+
|          2 byte            |        2 byte             |                    n byte                          |
+----------------------------+---------------------------+----------------------------------------------------+
*/

// 编码的时候：至少有tag 和 value，变长数据还需要 Length
// 接码之后，填充全部字段
type TlvObj struct {
	Tag    uint16 `json:"tag"`              //tag ID
	Length uint16 `json:"length,omitempty"` //定长数据可以不填写Length，变长必须填写
	Value  any    `json:"value"`            // 值。类型必须严格匹配
}

func (t *TlvObj) String() string {
	tagType, err := t.Check()
	if err != nil {
		return err.Error()
	}

	buf, err := t.encode()
	if err != nil {
		return err.Error()
	}

	sb := strings.Builder{}

	sb.WriteString("\t")
	sb.WriteString(fmt.Sprintf("Tag : %04x", t.Tag))
	sb.WriteString(fmt.Sprintf("\tType: %s", MapType[tagType].Name))
	sb.WriteString(fmt.Sprintf("\tLength: %04x", t.Length))
	if t.Length < 200 {
		sb.WriteString(fmt.Sprintf("\traw byte: %s", xutils.DumpHexRaw(buf[4:])))
	} else {
		sb.WriteString(fmt.Sprintf("\traw byte: toooooo big"))
	}

	return sb.String()
}

// 检查tag 是否合法
func (t *TlvObj) Check() (propType uint8, err error) {
	//获取属性信息
	info, err := GetPropByTag(t.Tag)
	if err != nil {
		err = errors.New(fmt.Sprintf("invalid prop tag: %02x", t.Tag))
		return
	}
	propType = info.Type

	return
}

func (t *TlvObj) encode() (buf []byte, err error) {
	var tagType uint8
	tagType, err = t.Check()
	bp := bintool.NewBufPacker(DefaultEndian)

	//检查属性类型是否合法
	ti, ok := MapType[tagType]
	if !ok {
		err = errors.New(fmt.Sprintf("invalid type : %d", tagType))
		return
	}

	//开始编码
	bp.PushUint16(t.Tag)

	// 检查数据长度是否合法
	if tagType == STRING || tagType == BYTES {
		// 变长数据在类型转换之后填充
	} else {
		t.Length = ti.Size
		bp.PushUint16(t.Length)
	}

	value := t.Value
	switch tagType {
	case INT8:
		if val, ok := value.(int8); ok {
			bp.PushInt8(val)
		} else if val2, ok := value.(float64); ok {
			bp.PushInt8(int8(val2))
		} else {
			err = errors.New("wrong int8 value")
		}
	case UINT8:
		if val, ok := value.(uint8); ok {
			bp.PushUint8(val)
		} else if val2, ok := value.(float64); ok {
			bp.PushUint8(uint8(val2))
		} else {
			err = errors.New("wrong uint8 value")
		}
	case INT16:
		if val, ok := value.(int16); ok {
			bp.PushInt16(val)
		} else if val2, ok := value.(float64); ok {
			bp.PushInt16(int16(val2))
		} else {
			err = errors.New("wrong int16 value")
		}
	case UINT16:
		if val, ok := value.(uint16); ok {
			bp.PushUint16(val)
		} else if val2, ok := value.(float64); ok {
			bp.PushUint16(uint16(val2))
		} else {
			err = errors.New("wrong uint16 value")
		}

	case INT32:
		if val, ok := value.(int32); ok {
			bp.PushInt32(val)
		} else if val2, ok := value.(float64); ok {
			bp.PushInt32(int32(val2))
		} else {
			err = errors.New("wrong int32 value")
		}

	case UINT32:
		if val, ok := value.(uint32); ok {
			bp.PushUint32(val)
		} else if val2, ok := value.(float64); ok {
			bp.PushUint32(uint32(val2))
		} else {
			err = errors.New("wrong uint32 value")
		}

	case INT64:
		if val, ok := value.(int64); ok {
			bp.PushInt64(val)
		} else if val2, ok := value.(float64); ok {
			bp.PushInt64(int64(val2))
		} else {
			err = errors.New("wrong int64 value")
		}

	case UINT64:
		if val, ok := value.(uint64); ok {
			bp.PushUint64(val)
		} else if val2, ok := value.(float64); ok {
			bp.PushUint64(uint64(val2))
		} else {
			err = errors.New("wrong uint64 value")
		}

	case FLOAT32:
		if val, ok := value.(float32); ok {
			bp.PushFloat32(val)
		} else if val2, ok := value.(float64); ok {
			bp.PushFloat32(float32(val2))
		} else {
			err = errors.New("wrong float32 value")
		}

	case FLOAT64:
		if val, ok := value.(float64); ok {
			bp.PushFloat64(val)
		} else {
			err = errors.New("wrong float64 value")
		}

		// 变长数据的长度信息默认就有
	case STRING:
		if val, ok := value.(string); ok {
			t.Length = uint16(len(val))
			bp.PushUint16(t.Length)
			bp.PushString(val)
		} else {
			err = errors.New("wrong string value")
		}

	case BYTES:
		if val1, ok1 := value.([]byte); ok1 {
			t.Length = uint16(len(val1))
			bp.PushUint16(t.Length)
			bp.PushBytes(val1)
		} else if val2, ok2 := value.(string); ok2 {
			//json 序列化之后，[]byte 会通过 base64编码，转为字符串
			dst, err := base64.StdEncoding.DecodeString(val2)
			if err != nil {
				err = errors.New("invalid base64 bytes value")
			} else {
				t.Length = uint16(len(dst))
				bp.PushUint16(t.Length)
				bp.PushBytes(dst)
			}
		}

	default:
		e := fmt.Sprintf("unsuport type, value2: %v, type : %s", value, reflect.TypeOf(value).String())
		err = errors.New(e)
		xlog.Error("invalid prop enc type: ", t)
	}
	//xlog.Sg.Info("encode tlv obj :", xutils.JSONString(t))
	//错误处理
	if err != nil {
		xlog.Info("encode failed: ", err.Error())
		return nil, err
	}

	return bp.Bytes(), nil
}

func (p *Packet) EncodeTlvObj(e xbin.PacketEncoder, t *TlvObj) (err error) {
	var tagType uint8
	tagType, err = t.Check()

	//检查属性类型是否合法
	ti, ok := MapType[tagType]
	if !ok {
		err = errors.New(fmt.Sprintf("invalid type : %d", tagType))
		return
	}

	//开始编码
	e.PutUint16(t.Tag)

	// 检查数据长度是否合法
	if tagType == STRING || tagType == BYTES {
		// 变长数据在类型转换之后填充
	} else {
		t.Length = ti.Size
		e.PutUint16(t.Length)
	}

	value := t.Value
	switch tagType {
	case INT8:
		if val, ok := value.(int8); ok {
			e.PutInt8(val)
		} else if val2, ok := value.(float64); ok {
			e.PutInt8(int8(val2))
		} else {
			err = errors.New("wrong int8 value")
		}
	case UINT8:
		if val, ok := value.(uint8); ok {
			e.PutUint8(val)
		} else if val2, ok := value.(float64); ok {
			e.PutUint8(uint8(val2))
		} else {
			err = errors.New("wrong uint8 value")
		}
	case INT16:
		if val, ok := value.(int16); ok {
			e.PutInt16(val)
		} else if val2, ok := value.(float64); ok {
			e.PutInt16(int16(val2))
		} else {
			err = errors.New("wrong int16 value")
		}
	case UINT16:
		if val, ok := value.(uint16); ok {
			e.PutUint16(val)
		} else if val2, ok := value.(float64); ok {
			e.PutUint16(uint16(val2))
		} else {
			err = errors.New("wrong uint16 value")
		}

	case INT32:
		if val, ok := value.(int32); ok {
			e.PutInt32(val)
		} else if val2, ok := value.(float64); ok {
			e.PutInt32(int32(val2))
		} else {
			err = errors.New("wrong int32 value")
		}

	case UINT32:
		if val, ok := value.(uint32); ok {
			e.PutUint32(val)
		} else if val2, ok := value.(float64); ok {
			e.PutUint32(uint32(val2))
		} else {
			err = errors.New("wrong uint32 value")
		}

	case INT64:
		if val, ok := value.(int64); ok {
			e.PutInt64(val)
		} else if val2, ok := value.(float64); ok {
			e.PutInt64(int64(val2))
		} else {
			err = errors.New("wrong int64 value")
		}

	case UINT64:
		if val, ok := value.(uint64); ok {
			e.PutUint64(val)
		} else if val2, ok := value.(float64); ok {
			e.PutUint64(uint64(val2))
		} else {
			err = errors.New("wrong uint64 value")
		}

	case FLOAT32:
		if val, ok := value.(float32); ok {
			e.PutFloat32(val)
		} else if val2, ok := value.(float64); ok {
			e.PutFloat32(float32(val2))
		} else {
			err = errors.New("wrong float32 value")
		}

	case FLOAT64:
		if val, ok := value.(float64); ok {
			e.PutFloat64(val)
		} else {
			err = errors.New("wrong float64 value")
		}

		// 变长数据的长度信息默认就有
	case STRING:
		if val, ok := value.(string); ok {
			t.Length = uint16(len(val))
			e.PutUint16(t.Length)
			_ = e.PutRawString(val)
		} else {
			err = errors.New("wrong string value")
		}

	case BYTES:
		if val1, ok1 := value.([]byte); ok1 {
			t.Length = uint16(len(val1))
			e.PutUint16(t.Length)
			_ = e.PutRawBytes(val1)
		} else if val2, ok2 := value.(string); ok2 {
			//json 序列化之后，[]byte 会通过 base64编码，转为字符串
			dst, err := base64.StdEncoding.DecodeString(val2)
			if err != nil {
				xlog.Error("parse string to []byte failed:", err.Error())
				err = errors.New("wrong bytes value")
			} else {
				t.Length = uint16(len(dst))
				e.PutUint16(t.Length)
				_ = e.PutRawBytes(dst)
			}
		}

	default:
		e := fmt.Sprintf("unsuport type, value2: %v, type : %s", value, reflect.TypeOf(value).String())
		err = errors.New(e)
		xlog.Error(err.Error())

	}

	// xlog.Info("encode tlv obj: ", t)

	//错误处理
	if err != nil {
		xlog.Info("encode failed: ", err.Error())
		return err
	}

	return nil
}

func (p *Packet) DecodeTlvObj(d xbin.PacketDecoder) (t *TlvObj, err error) {
	//fmt.Printf("progress : %d ,remain: %d\n", p.progress, p.total-p.progress)

	t = &TlvObj{}
	t.Tag, err = d.Uint16()
	if err != nil {
		return
	}
	p.progress += 2

	t.Length, err = d.Uint16()
	if err != nil {
		return
	}
	p.progress += 2

	fmt.Printf("progress : %d , tag :%04x, len : %04x\n", p.progress, t.Tag, t.Length)

	if t.Length > p.total-p.progress {
		err = errors.New(fmt.Sprintf("break 003 ...... remain: %d", p.total-p.progress))
		return
	}

	var tagType uint8
	prop, e := GetPropByTag(t.Tag)
	if e != nil {
		tagType = BYTES //如果不存在此prop，则当做 bytes 类型解析。以保证解析不出错。
	} else {
		tagType = prop.Type
	}

	switch tagType {
	case INT8:
		t.Value, err = d.Int8()
	case UINT8:
		t.Value, err = d.Uint8()
	case INT16:
		t.Value, err = d.Int16()
	case UINT16:
		t.Value, err = d.Uint16()
	case INT32:
		t.Value, err = d.Int32()
	case UINT32:
		t.Value, err = d.Uint32()
	case INT64:
		t.Value, err = d.Int64()
	case UINT64:
		t.Value, err = d.Uint64()
	case FLOAT32:
		t.Value, err = d.Float32()
	case FLOAT64:
		t.Value, err = d.Float64()
	case STRING:
		t.Value, err = d.StringWithLen(int(t.Length))
	case BYTES:
		t.Value, err = d.BytesWithLen(int(t.Length))

	default:
		err = errors.New("tlv decode:invalid prop enc type")
		xlog.Error(err.Error())

		return
	}

	p.progress += t.Length

	//错误处理
	if err != nil {
		xlog.Error(err.Error())
		return
	}

	return
}
