/**
 *
 *    ____          __
 *   / __/__ ____ _/ /__
 *  / _// _ `/ _ `/ / -_)
 * /___/\_,_/\_, /_/\__/
 *         /___/
 *
 *
 * generate by http://patorjk.com/software/taag/#p=display&f=Small%20Slant&t=Eagle
 */
package main

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"bs.com/ebox/cmd"
	"bs.com/ebox/config"
	"bs.com/ebox/global"
	"bs.com/ebox/pkg/xutils"
	"github.com/spf13/cobra"
)

func checkDir() {
	cfg := config.Get()

	//检查文件目录，创建必须的目录
	if err := xutils.MkDirIfNotExist(cfg.Misc.CDNDir); err != nil {
		fmt.Println("check CDNDir err")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathLog); err != nil {
		fmt.Println("check PathLog err")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathFirmware); err != nil {
		fmt.Println("check PathFirmware err")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathImage); err != nil {
		fmt.Println("check PathImage err")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathExport); err != nil {
		fmt.Println("check PathExport err")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathTmp); err != nil {
		fmt.Println("check PathTmp err")
		os.Exit(1)
	}

	////ota相关的脚本检查
	//if err := tasks.OtaPreCheck(); err != nil {
	//	fmt.Println("invalid ota scripts")
	//	os.Exit(1)
	//}
}

var configFile string

func initConfig() {
	fmt.Println("we use config file : ", configFile)
	//检查配置文件
	if _, err := os.Stat(configFile); err != nil {
		configFile = filepath.Join(xutils.GetExecPath(), configFile)
		if _, err = os.Stat(configFile); err != nil {
			fmt.Println("config file not found: ", configFile)
			os.Exit(1)
		}
	}

	//解析配置文件
	config.ParseConfig(configFile)

	//初始检查
	checkDir()

	//全局容器
	global.InitGlobal()
}

func main() {
	var rootCmd = &cobra.Command{
		Use:   "ebox",
		Short: "ebox iot platform",
		Long:  "service : gateway proxy、iot、sl651.",
		Args: func(cmd *cobra.Command, args []string) error {
			if len(args) < 1 {
				_ = cmd.Usage()
				return errors.New("show usage")
			}
			return nil
		},
	}

	// 使用方法： ./build/ebox_linux.bin gw --config config.yaml
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "config.yaml", "select configuration file")

	// 设置配置初始化函数，在命令执行前调用
	rootCmd.PersistentPreRun = func(cmd *cobra.Command, args []string) {
		initConfig()
	}

	//添加命令行入口
	// rootCmd.AddCommand(cmd.GW)
	rootCmd.AddCommand(cmd.Shui2022)
	rootCmd.AddCommand(cmd.Shui651)
	rootCmd.AddCommand(cmd.Shui206)
	rootCmd.AddCommand(cmd.Shui427)
	rootCmd.AddCommand(cmd.Proxy)

	rootCmd.AddCommand(cmd.Mtrip)
	rootCmd.AddCommand(cmd.UDPS)
	rootCmd.AddCommand(cmd.TCPS)

	_ = rootCmd.Execute()
}
